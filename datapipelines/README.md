# TractionX Data Pipeline Service

TractionX's standalone data pipeline service orchestrates ingestion, enrichment, merging, and storage of all structured/unstructured data relevant to companies, founders, news, and signals—across multiple sources (form submissions, enrichment APIs, scrapers).

## Overview

This service provides:
- **Asynchronous pipelines** using RQ (Redis Queue)
- **Core data models** for company, founder, news, and embeddings
- **Enrichment flows** for Company (Clay), Founder (PDL), News (Bing News/API), Embedding (OpenAI/Qdrant)
- **Canonical ETL** for merging and structuring data
- **Storage** in AWS RDS (Postgres), S3, and Qdrant
- **Webhook support** for Clay and other providers
- **Modular architecture** - easy to add more pipelines/APIs

## Architecture

```
Deal Submission → Pipeline Trigger → Enrichment Jobs → ETL Merge → Storage
     ↓                    ↓              ↓              ↓         ↓
Form/API/Webhook → RQ Job Queue → Clay/PDL/News → Canonical → RDS/S3/Qdrant
```

## Directory Structure

```
datapipelines/
├── app/                    # All Python code here
│   ├── main.py             # FastAPI app entrypoint
│   ├── worker.py           # RQ worker entrypoint
│   ├── pipelines/          # Individual pipeline modules
│   │   ├── company.py      # Clay company enrichment
│   │   ├── founder.py      # PDL founder enrichment
│   │   ├── news.py         # Bing News aggregation
│   │   └── embedding.py    # OpenAI + Qdrant embeddings
│   ├── models/             # Pydantic schemas
│   ├── storage/            # Storage layer abstractions
│   ├── tasks/              # RQ job definitions
│   ├── webhooks/           # FastAPI webhook endpoints
│   ├── configs/            # Configuration management
│   └── utils.py            # Utility functions
├── tests/                  # Test suite
├── pyproject.toml          # Poetry dependencies
├── poetry.lock             # Locked dependencies
├── Dockerfile              # Container definition
├── docker-compose.yml      # Multi-service orchestration
├── test_imports.py         # Import verification script
├── run_tests.sh            # Complete test script
└── README.md               # This file
```

## Quick Start

### Prerequisites
- Python 3.11+
- Redis (for RQ)
- PostgreSQL (for RDS storage)
- S3-compatible storage
- Qdrant vector database

### Installation

1. **Install Poetry (if not already installed):**
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

2. **Install dependencies:**
```bash
cd datapipelines
poetry install
```

3. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start services with Docker:**
```bash
docker-compose up -d
```

### Development

**Quick setup:**
```bash
python dev.py setup
```

**Complete test and setup:**
```bash
./run_tests.sh
```

**Test imports locally:**
```bash
make test
```

**Test imports in Docker:**
```bash
make test-docker
```

**Start the API server (with hot reload):**
```bash
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload
```

**Start the worker process (with hot reload):**
```bash
poetry run watchmedo auto-restart --directory=./app --pattern="*.py" --recursive -- poetry run python app/worker.py
```

**Start all services with Docker:**
```bash
docker compose up --build
```

## Pipeline Flow

### 1. Trigger
- Deal submission (manual, webhook, or API)
- Input: form data, deal/company ID

### 2. Company Enrichment (Clay)
- Async RQ job triggered
- Upsert to RDS (companies), store raw in S3

### 3. Founder Enrichment (PDL)  
- Triggered after Clay completion
- Upsert to RDS (founders), store raw in S3

### 4. News Aggregation (Bing/API)
- Parallel job for news collection
- Upsert news records, store raw data

### 5. Embedding Generation (OpenAI/Qdrant)
- Generate embeddings for text fields
- Store vectors in Qdrant for semantic search

### 6. ETL Merge
- Deterministic merge: form data > enrichment data
- Source tracking on every attribute
- Output canonical schema to RDS

## API Endpoints

### Webhooks
- `POST /webhooks/clay` - Clay enrichment webhook
- `POST /webhooks/generic` - Generic pipeline trigger

### Pipeline Management
- `POST /api/pipelines/trigger` - Manual pipeline trigger
- `GET /api/pipelines/status/{job_id}` - Job status check
- `GET /api/pipelines/stats` - Pipeline statistics

## Configuration

Key environment variables:

```bash
# Redis (RQ)
REDIS_URL=redis://localhost:6379/0

# Database
DATABASE_URL=postgresql://user:pass@localhost/tractionx

# S3 Storage  
AWS_ACCESS_KEY_ID=your_key
AWS_SECRET_ACCESS_KEY=your_secret
S3_BUCKET=tractionx-data

# Qdrant Vector DB
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_key

# External APIs
CLAY_API_KEY=your_clay_key
PDL_API_KEY=your_pdl_key
BING_NEWS_API_KEY=your_bing_key
OPENAI_API_KEY=your_openai_key
```

## Troubleshooting

### Redis Import Errors

If you encounter `ModuleNotFoundError: No module named 'redis'`:

1. **Clean rebuild Docker images:**
   ```bash
   docker-compose down
   docker-compose build --no-cache
   ```

2. **Test Redis imports:**
   ```bash
   ./fix_and_test.sh
   ```

3. **Check Redis version:**
   ```bash
   poetry show redis
   # Should show redis 5.0+ with asyncio support
   ```

### Validation Checklist

Use this checklist to quickly diagnose and fix import errors:

1. **Project Structure**
   - [ ] All source code in app/ folder
   - [ ] Dockerfile and docker-compose.yml at project root
   - [ ] pyproject.toml and poetry.lock at project root

2. **Poetry Dependencies**
   - [ ] Run `poetry lock` to ensure poetry.lock is up to date
   - [ ] Verify `rq` and `redis` are in pyproject.toml
   - [ ] Run `poetry install` to update local environment

3. **Docker Setup**
   - [ ] Dockerfile uses Python 3.11 slim image
   - [ ] Poetry virtualenvs are disabled
   - [ ] Dependencies installed before copying code
   - [ ] No volume mounts for worker service in production

4. **Verification Steps**
   - [ ] Run `python test_imports.py` locally
   - [ ] Check container logs: `docker-compose logs api`
   - [ ] Check container logs: `docker-compose logs worker`
   - [ ] Exec into containers to test imports:
     ```bash
     docker-compose exec worker python -c "import rq; print('RQ version:', rq.__version__)"
     docker-compose exec worker python -c "import redis; print('Redis version:', redis.__version__)"
     ```

### Common Issues

- **Volume mounts**: Avoid mounting local code in production Docker containers
- **Poetry cache**: Clear Poetry cache if dependencies seem outdated
- **Redis connection**: Ensure Redis service starts before worker/API services

### Docker Best Practices

- Always use `--no-cache` when changing dependencies
- Remove volume mounts for production deployments
- Use `poetry install --only=main` for production builds

## Adding New Pipelines

1. **Create pipeline module** in `pipelines/`
2. **Define data models** in `models/`
3. **Add RQ tasks** in `tasks/`
4. **Register handlers** in `worker.py`
5. **Add tests** in `tests/`

Example:
```python
# pipelines/my_enrichment.py
from .base import BasePipeline

class MyEnrichmentPipeline(BasePipeline):
    async def process(self, data):
        # Your enrichment logic
        return enriched_data
```

## Testing

```bash
# Run all tests
python dev.py test
# or
poetry run pytest

# Run with coverage
python dev.py test-cov
# or
poetry run pytest --cov=datapipelines --cov-report=html

# Run linting
python dev.py lint
# or
poetry run black . && poetry run isort . && poetry run mypy .
```

## Deployment

### Docker
```bash
docker build -t tractionx-datapipelines .
docker run -d --name datapipelines tractionx-datapipelines
```

### Production
- Deploy webhook service (FastAPI) to cloud platform
- Deploy worker processes with auto-scaling
- Configure monitoring and alerting
- Set up log aggregation

## Monitoring

- **Structured logging** (JSON format)
- **RQ job monitoring** via Redis
- **Pipeline metrics** and success rates
- **Error tracking** and alerting
- **Performance monitoring** for enrichment APIs

## Best Practices

- All config via environment variables
- Modular pipeline design with clear interfaces
- Comprehensive error handling and retry logic
- Structured logging for debugging
- Test coverage for all pipelines
- Documentation for all functions
- Standard Python formatting (black, isort, flake8)
