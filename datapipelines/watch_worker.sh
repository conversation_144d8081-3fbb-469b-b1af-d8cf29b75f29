#!/bin/bash
# <PERSON>ript to watch for changes and restart the worker

cd /app
echo "Starting worker with auto-reload..."

# Function to run the worker
run_worker() {
    echo "Starting worker process..."
    python -m app.worker &
    WORKER_PID=$!
    echo "Worker started with PID: $WORKER_PID"
}

# Function to handle cleanup
cleanup() {
    echo "Stopping worker process..."
    if [ -n "$WORKER_PID" ]; then
        kill $WORKER_PID 2>/dev/null
    fi
    exit 0
}

# Set up trap for cleanup
trap cleanup SIGINT SIGTERM

# Start the worker initially
run_worker

# Watch for changes and restart
while true; do
    echo "Watching for changes in app/ directory..."

    # Use inotifywait if available, otherwise just sleep
    if command -v inotifywait >/dev/null 2>&1; then
        inotifywait -r -e modify,create,delete ./app/
    else
        # Simple polling as fallback
        sleep 15
        CHANGES=$(find ./app -type f -name "*.py" -newer /tmp/last_check 2>/dev/null)
        touch /tmp/last_check

        if [ -n "$CHANGES" ]; then
            echo "Changes detected in: $CHANGES"
        else
            continue
        fi
    fi

    echo "Changes detected, restarting worker..."

    # Kill the current worker process
    if [ -n "$WORKER_PID" ]; then
        kill $WORKER_PID 2>/dev/null
        wait $WORKER_PID 2>/dev/null
    fi

    # Start the worker again
    run_worker
done
