services:
  api:
    build: .
    command: poetry run uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload
    ports:
      - "8001:8001"
    volumes:
      - .:/app          # only for dev/hot reload, not for prod
    environment:
      - PYTHONPATH=/app
      - ENV=dev
    env_file:
      - .env

  worker:
    build: .
    command: bash -c "chmod +x /app/watch_worker.sh && /app/watch_worker.sh"
    volumes:
      - .:/app          # For development to enable hot reload
    environment:
      - PYTHONPATH=/app
      - ENV=dev
    env_file:
      - .env



