.PHONY: install start test test-verbose test-cov test-html lint format clean docker-up docker-down install-dev install-prod start-worker docker-up-d docker-logs docker-restart dev-all

install: install-prod install-dev

install-prod:
	poetry install --no-dev

install-dev:
	poetry install --with dev

start:
	poetry run uvicorn main:app --reload --host 0.0.0.0 --port 8001

start-worker:
	poetry run python worker.py --watch

start-worker-no-watch:
	poetry run python worker.py --no-watch

test:
	poetry run pytest

test-verbose:
	poetry run pytest -v

test-cov:
	poetry run pytest --cov=datapipelines

test-html:
	poetry run pytest --cov=datapipelines --cov-report=html

lint:
	poetry run black .
	poetry run isort .
	poetry run mypy .

format:
	poetry run black .
	poetry run isort .

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +

# Docker commands
docker-up:
	docker-compose up

docker-up-d:
	docker compose up -d

docker-down:
	docker compose down

docker-logs:
	docker compose logs -f

docker-restart:
	docker compose restart

docker-build:
	docker compose build

docker-clean:
	docker compose down -v
	docker system prune -f

# Development helpers
dev-setup: install
	cp .env.example .env
	@echo "Please edit .env file with your configuration"

dev-start: start

dev-worker: start-worker

dev-all:
	make docker compose up --build -d

# Production commands
prod-build:
	docker build -t tractionx-datapipelines .

prod-run:
	docker run -d --name datapipelines -p 8001:8001 tractionx-datapipelines

# Health check
health:
	curl -f http://localhost:8001/health || echo "Service not running"

# Test imports and functionality
test:
	poetry run python test_imports.py

# Test in Docker
test-docker:
	docker compose exec api python test_imports.py

# Test worker in Docker
test-worker-docker:
	docker compose exec worker python test_imports.py

# Database commands
db-init:
	poetry run python -c "from storage.rds_storage import RDSStorage; import asyncio; asyncio.run(RDSStorage().initialize())"

# Monitoring
logs:
	docker compose logs -f datapipeline-api datapipeline-worker

stats:
	curl -s http://localhost:8001/api/v1/pipelines/stats | python -m json.tool
