# Use official Python slim base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install Poetry
RUN pip install poetry==1.7.1

# Disable Poetry virtualenvs
RUN poetry config virtualenvs.create false

# Copy only pyproject.toml and poetry.lock first for caching
COPY pyproject.toml poetry.lock* ./

# Install dependencies
RUN poetry install --no-interaction --no-ansi --no-root

# Copy the rest of the source code
COPY . .

# Set Python path
ENV PYTHONPATH=/app

# Default command (will be overridden in docker-compose.yml)
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]