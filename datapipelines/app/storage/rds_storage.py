"""
RDS (PostgreSQL) storage implementation for TractionX Data Pipeline Service.
"""

import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import asyncpg
from asyncpg import Pool

from app.configs import get_logger, settings
from app.models.base import BaseModel
from .interfaces import RelationalStorageInterface


class RDSStorage(RelationalStorageInterface):
    """PostgreSQL storage implementation."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.pool: Optional[Pool] = None
        self.connection_string = settings.database_connection_string
    
    async def initialize(self) -> None:
        """Initialize the database connection pool."""
        try:
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=1,
                max_size=settings.DB_POOL_SIZE,
                max_inactive_connection_lifetime=300
            )
            self.logger.info("RDS storage initialized successfully")
            
            # Create core tables if they don't exist
            await self._create_core_tables()
            
        except Exception as e:
            self.logger.error(f"Failed to initialize RDS storage: {e}")
            raise
    
    async def cleanup(self) -> None:
        """Clean up database resources."""
        if self.pool:
            await self.pool.close()
            self.logger.info("RDS storage cleaned up")
    
    async def health_check(self) -> bool:
        """Check if database is healthy."""
        try:
            if not self.pool:
                return False
            
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True
            
        except Exception as e:
            self.logger.error(f"RDS health check failed: {e}")
            return False
    
    async def create_table(self, table_name: str, schema: Dict[str, Any]) -> bool:
        """Create a table with the given schema."""
        try:
            # Convert schema to SQL DDL
            columns = []
            for column_name, column_def in schema.items():
                columns.append(f"{column_name} {column_def}")
            
            ddl = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"
            
            async with self.pool.acquire() as conn:
                await conn.execute(ddl)
            
            self.logger.info(f"Created table {table_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create table {table_name}: {e}")
            return False
    
    async def insert(self, table_name: str, data: Union[Dict[str, Any], BaseModel]) -> str:
        """Insert data and return the ID."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()
            
            # Add timestamp if not present
            if "created_at" not in data_dict:
                data_dict["created_at"] = datetime.utcnow()
            
            # Prepare SQL
            columns = list(data_dict.keys())
            placeholders = [f"${i+1}" for i in range(len(columns))]
            values = [self._serialize_value(data_dict[col]) for col in columns]
            
            sql = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
                RETURNING id
            """
            
            async with self.pool.acquire() as conn:
                record_id = await conn.fetchval(sql, *values)
            
            self.logger.debug(f"Inserted record into {table_name} with ID {record_id}")
            return str(record_id)
            
        except Exception as e:
            self.logger.error(f"Failed to insert into {table_name}: {e}")
            raise
    
    async def update(self, table_name: str, id: str, data: Union[Dict[str, Any], BaseModel]) -> bool:
        """Update data by ID."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()
            
            # Add updated timestamp
            data_dict["updated_at"] = datetime.utcnow()
            
            # Prepare SQL
            set_clauses = []
            values = []
            for i, (column, value) in enumerate(data_dict.items()):
                set_clauses.append(f"{column} = ${i+1}")
                values.append(self._serialize_value(value))
            
            values.append(id)  # For WHERE clause
            
            sql = f"""
                UPDATE {table_name}
                SET {', '.join(set_clauses)}
                WHERE id = ${len(values)}
            """
            
            async with self.pool.acquire() as conn:
                result = await conn.execute(sql, *values)
            
            # Check if any rows were updated
            rows_updated = int(result.split()[-1])
            success = rows_updated > 0
            
            if success:
                self.logger.debug(f"Updated record in {table_name} with ID {id}")
            else:
                self.logger.warning(f"No record found to update in {table_name} with ID {id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to update {table_name} record {id}: {e}")
            return False
    
    async def upsert(self, table_name: str, data: Union[Dict[str, Any], BaseModel], key_fields: List[str]) -> str:
        """Insert or update data based on key fields."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()
            
            # Add timestamps
            now = datetime.utcnow()
            if "created_at" not in data_dict:
                data_dict["created_at"] = now
            data_dict["updated_at"] = now
            
            # Build conflict resolution
            conflict_columns = ', '.join(key_fields)
            update_clauses = []
            for column in data_dict.keys():
                if column not in key_fields:
                    update_clauses.append(f"{column} = EXCLUDED.{column}")
            
            # Prepare SQL
            columns = list(data_dict.keys())
            placeholders = [f"${i+1}" for i in range(len(columns))]
            values = [self._serialize_value(data_dict[col]) for col in columns]
            
            sql = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
                ON CONFLICT ({conflict_columns})
                DO UPDATE SET {', '.join(update_clauses)}
                RETURNING id
            """
            
            async with self.pool.acquire() as conn:
                record_id = await conn.fetchval(sql, *values)
            
            self.logger.debug(f"Upserted record in {table_name} with ID {record_id}")
            return str(record_id)
            
        except Exception as e:
            self.logger.error(f"Failed to upsert into {table_name}: {e}")
            raise
    
    async def get_by_id(self, table_name: str, id: str) -> Optional[Dict[str, Any]]:
        """Get data by ID."""
        try:
            sql = f"SELECT * FROM {table_name} WHERE id = $1"
            
            async with self.pool.acquire() as conn:
                record = await conn.fetchrow(sql, id)
            
            if record:
                return dict(record)
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get record from {table_name} with ID {id}: {e}")
            return None
    
    async def get_by_fields(self, table_name: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get data by field filters."""
        try:
            # Build WHERE clause
            where_clauses = []
            values = []
            for i, (column, value) in enumerate(filters.items()):
                where_clauses.append(f"{column} = ${i+1}")
                values.append(self._serialize_value(value))
            
            where_clause = " AND ".join(where_clauses) if where_clauses else "TRUE"
            sql = f"SELECT * FROM {table_name} WHERE {where_clause}"
            
            async with self.pool.acquire() as conn:
                records = await conn.fetch(sql, *values)
            
            return [dict(record) for record in records]
            
        except Exception as e:
            self.logger.error(f"Failed to get records from {table_name}: {e}")
            return []
    
    async def delete(self, table_name: str, id: str) -> bool:
        """Delete data by ID."""
        try:
            sql = f"DELETE FROM {table_name} WHERE id = $1"
            
            async with self.pool.acquire() as conn:
                result = await conn.execute(sql, id)
            
            rows_deleted = int(result.split()[-1])
            success = rows_deleted > 0
            
            if success:
                self.logger.debug(f"Deleted record from {table_name} with ID {id}")
            else:
                self.logger.warning(f"No record found to delete from {table_name} with ID {id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to delete from {table_name} record {id}: {e}")
            return False
    
    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a custom query."""
        try:
            async with self.pool.acquire() as conn:
                if params:
                    records = await conn.fetch(query, *params.values())
                else:
                    records = await conn.fetch(query)
            
            return [dict(record) for record in records]
            
        except Exception as e:
            self.logger.error(f"Failed to execute query: {e}")
            return []
    
    def _serialize_value(self, value: Any) -> Any:
        """Serialize value for database storage."""
        if isinstance(value, (dict, list)):
            return json.dumps(value)
        elif isinstance(value, datetime):
            return value
        else:
            return value
    
    async def _create_core_tables(self) -> None:
        """Create core tables for the pipeline."""
        tables = {
            "companies": {
                "id": "SERIAL PRIMARY KEY",
                "company_id": "VARCHAR(255) UNIQUE NOT NULL",
                "org_id": "VARCHAR(255) NOT NULL",
                "name": "VARCHAR(500)",
                "data": "JSONB",
                "source": "VARCHAR(100)",
                "created_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
            },
            "founders": {
                "id": "SERIAL PRIMARY KEY", 
                "founder_id": "VARCHAR(255) UNIQUE NOT NULL",
                "company_id": "VARCHAR(255) NOT NULL",
                "org_id": "VARCHAR(255) NOT NULL",
                "data": "JSONB",
                "source": "VARCHAR(100)",
                "created_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
            },
            "news": {
                "id": "SERIAL PRIMARY KEY",
                "company_id": "VARCHAR(255) NOT NULL",
                "org_id": "VARCHAR(255) NOT NULL", 
                "data": "JSONB",
                "created_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
            },
            "pipeline_jobs": {
                "id": "SERIAL PRIMARY KEY",
                "job_id": "VARCHAR(255) UNIQUE NOT NULL",
                "pipeline_name": "VARCHAR(255) NOT NULL",
                "status": "VARCHAR(50) NOT NULL",
                "metadata": "JSONB",
                "created_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
            }
        }
        
        for table_name, schema in tables.items():
            await self.create_table(table_name, schema)
