"""
Storage interface definitions for TractionX Data Pipeline Service.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from models.base import BaseModel


class StorageInterface(ABC):
    """Abstract interface for storage operations."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the storage connection."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up storage resources."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if storage is healthy."""
        pass


class RelationalStorageInterface(StorageInterface):
    """Interface for relational database storage (RDS)."""
    
    @abstractmethod
    async def create_table(self, table_name: str, schema: Dict[str, Any]) -> bool:
        """Create a table with the given schema."""
        pass
    
    @abstractmethod
    async def insert(self, table_name: str, data: Union[Dict[str, Any], BaseModel]) -> str:
        """Insert data and return the ID."""
        pass
    
    @abstractmethod
    async def update(self, table_name: str, id: str, data: Union[Dict[str, Any], BaseModel]) -> bool:
        """Update data by ID."""
        pass
    
    @abstractmethod
    async def upsert(self, table_name: str, data: Union[Dict[str, Any], BaseModel], key_fields: List[str]) -> str:
        """Insert or update data based on key fields."""
        pass
    
    @abstractmethod
    async def get_by_id(self, table_name: str, id: str) -> Optional[Dict[str, Any]]:
        """Get data by ID."""
        pass
    
    @abstractmethod
    async def get_by_fields(self, table_name: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get data by field filters."""
        pass
    
    @abstractmethod
    async def delete(self, table_name: str, id: str) -> bool:
        """Delete data by ID."""
        pass
    
    @abstractmethod
    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a custom query."""
        pass


class ObjectStorageInterface(StorageInterface):
    """Interface for object storage (S3)."""
    
    @abstractmethod
    async def put_object(self, key: str, data: Union[bytes, str, Dict[str, Any]], metadata: Optional[Dict[str, str]] = None) -> bool:
        """Store an object."""
        pass
    
    @abstractmethod
    async def get_object(self, key: str) -> Optional[bytes]:
        """Retrieve an object."""
        pass
    
    @abstractmethod
    async def get_object_metadata(self, key: str) -> Optional[Dict[str, str]]:
        """Get object metadata."""
        pass
    
    @abstractmethod
    async def delete_object(self, key: str) -> bool:
        """Delete an object."""
        pass
    
    @abstractmethod
    async def list_objects(self, prefix: str = "", limit: int = 1000) -> List[str]:
        """List objects with optional prefix."""
        pass
    
    @abstractmethod
    async def object_exists(self, key: str) -> bool:
        """Check if object exists."""
        pass
    
    @abstractmethod
    async def generate_presigned_url(self, key: str, expiration: int = 3600) -> str:
        """Generate a presigned URL for object access."""
        pass


class VectorStorageInterface(StorageInterface):
    """Interface for vector database storage (Qdrant)."""
    
    @abstractmethod
    async def create_collection(self, collection_name: str, vector_size: int, distance_metric: str = "cosine") -> bool:
        """Create a vector collection."""
        pass
    
    @abstractmethod
    async def insert_vectors(self, collection_name: str, vectors: List[Dict[str, Any]]) -> bool:
        """Insert vectors with metadata."""
        pass
    
    @abstractmethod
    async def update_vector(self, collection_name: str, vector_id: str, vector: List[float], metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Update a vector by ID."""
        pass
    
    @abstractmethod
    async def search_vectors(self, collection_name: str, query_vector: List[float], limit: int = 10, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for similar vectors."""
        pass
    
    @abstractmethod
    async def get_vector(self, collection_name: str, vector_id: str) -> Optional[Dict[str, Any]]:
        """Get a vector by ID."""
        pass
    
    @abstractmethod
    async def delete_vector(self, collection_name: str, vector_id: str) -> bool:
        """Delete a vector by ID."""
        pass
    
    @abstractmethod
    async def delete_vectors(self, collection_name: str, filters: Dict[str, Any]) -> int:
        """Delete vectors matching filters."""
        pass
    
    @abstractmethod
    async def get_collection_info(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """Get collection information."""
        pass


class CacheStorageInterface(StorageInterface):
    """Interface for cache storage (Redis)."""
    
    @abstractmethod
    async def set(self, key: str, value: Union[str, bytes, Dict[str, Any]], ttl: Optional[int] = None) -> bool:
        """Set a cache value."""
        pass
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Union[str, bytes]]:
        """Get a cache value."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete a cache value."""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        pass
    
    @abstractmethod
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration for a key."""
        pass
    
    @abstractmethod
    async def keys(self, pattern: str = "*") -> List[str]:
        """Get keys matching pattern."""
        pass
    
    @abstractmethod
    async def flush(self) -> bool:
        """Flush all cache data."""
        pass
