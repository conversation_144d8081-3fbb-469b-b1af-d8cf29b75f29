"""
Utility functions for TractionX Data Pipeline Service.
"""

import hashlib
import json
import time
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse
import redis.asyncio as redis
from redis.asyncio import Redis
from app.configs import settings, get_logger

logger = get_logger(__name__)


class QueueClient:
    """Redis queue client for enqueueing jobs."""
    
    def __init__(self, redis_url: str = None):
        self.redis_url = redis_url or settings.redis_connection_string
        self._connection = None
    
    @property
    async def connection(self) -> Redis:
        """Get Redis connection (lazy initialization)."""
        if not self._connection:
            self._connection = redis.from_url(self.redis_url, decode_responses=True)
        return self._connection
    
    async def enqueue_job(self, 
                         queue_name: str,
                         job_function: str, 
                         job_data: Dict[str, Any],
                         timeout: int = 300,
                         max_retries: int = 3) -> str:
        """Enqueue a job for processing."""
        
        job_id = f"{queue_name}:{int(time.time() * 1000)}:{uuid.uuid4().hex[:8]}"
        
        # Store job metadata
        job_metadata = {
            "id": job_id,
            "function": job_function,
            "data": job_data,
            "queue": queue_name,
            "status": "queued",
            "created_at": time.time(),
            "timeout": timeout,
            "retry_count": 0,
            "max_retries": max_retries
        }
        
        queue_key = f"queue:{queue_name}"
        job_key = f"job:{job_id}"
        
        try:
            conn = await self.connection
            
            # Store job metadata
            await conn.hset(job_key, mapping={
                k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
                for k, v in job_metadata.items()
            })
            
            # Set job expiration (24 hours)
            await conn.expire(job_key, 86400)
            
            # Add job to queue
            await conn.lpush(queue_key, job_id)
            
            logger.info(f"Enqueued job {job_id}: {job_function} to queue {queue_name}")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to enqueue job: {e}")
            raise
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status and metadata."""
        job_key = f"job:{job_id}"
        
        try:
            conn = await self.connection
            job_data = await conn.hgetall(job_key)
            
            if not job_data:
                return {"status": "not_found"}
            
            # Parse JSON fields
            for key in ["data", "result", "error"]:
                if key in job_data and job_data[key]:
                    try:
                        job_data[key] = json.loads(job_data[key])
                    except json.JSONDecodeError:
                        pass
            
            return job_data
            
        except Exception as e:
            logger.error(f"Failed to get job status: {e}")
            return {"status": "error", "error": str(e)}
    
    async def update_job_status(self, job_id: str, status: str, **updates) -> None:
        """Update job status and other fields."""
        job_key = f"job:{job_id}"
        
        try:
            conn = await self.connection
            
            # Prepare updates
            update_data = {"status": status}
            update_data.update(updates)
            
            # Convert complex objects to JSON
            for key, value in update_data.items():
                if isinstance(value, (dict, list)):
                    update_data[key] = json.dumps(value)
                else:
                    update_data[key] = str(value)
            
            await conn.hset(job_key, mapping=update_data)
            
        except Exception as e:
            logger.error(f"Failed to update job status: {e}")
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a job."""
        try:
            await self.update_job_status(job_id, "cancelled", cancelled_at=time.time())
            return True
        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            return False
    
    async def enqueue(self, 
                     function_name: str, 
                     *args, 
                     queue: str = "default",
                     max_retries: int = 3,
                     **kwargs) -> str:
        """Legacy enqueue method for backward compatibility."""
        job_data = {"args": list(args), "kwargs": kwargs}
        return await self.enqueue_job(queue, function_name, job_data, max_retries=max_retries)
    
    async def get_queue_stats(self, queue: str = "default") -> Dict[str, int]:
        """Get queue statistics."""
        queue_key = f"queue:{queue}"
        stats_key = f"stats:{queue}"
        
        try:
            conn = await self.connection
            pending = await conn.llen(queue_key)
            stats = await conn.hgetall(stats_key)
            
            return {
                "pending": pending,
                "completed": int(stats.get("completed", 0)),
                "failed": int(stats.get("failed", 0))
            }
        except Exception as e:
            logger.error(f"Failed to get queue stats: {e}")
            return {"pending": 0, "completed": 0, "failed": 0}
    
    async def close(self):
        """Close the Redis connection."""
        if self._connection:
            await self._connection.aclose()
            self._connection = None


# Global queue client instance
queue_client = QueueClient()


def generate_job_id() -> str:
    """Generate a unique job ID."""
    return str(uuid.uuid4())


def generate_hash(data: Union[str, Dict[str, Any]]) -> str:
    """Generate a hash for data deduplication."""
    if isinstance(data, dict):
        # Sort keys for consistent hashing
        data_str = json.dumps(data, sort_keys=True)
    else:
        data_str = str(data)
    
    return hashlib.sha256(data_str.encode()).hexdigest()


def normalize_company_name(name: str) -> str:
    """Normalize company name for better matching."""
    if not name:
        return ""
    
    # Remove common suffixes
    suffixes = [
        "inc", "inc.", "incorporated",
        "llc", "l.l.c.", "limited liability company",
        "ltd", "ltd.", "limited",
        "corp", "corp.", "corporation",
        "co", "co.", "company",
        "pte", "pte.", "private limited"
    ]
    
    normalized = name.lower().strip()
    
    for suffix in suffixes:
        if normalized.endswith(f" {suffix}"):
            normalized = normalized[:-len(suffix)-1].strip()
    
    return normalized


def extract_domain_from_url(url: str) -> Optional[str]:
    """Extract domain from URL."""
    if not url:
        return None
    
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove www. prefix
        if domain.startswith("www."):
            domain = domain[4:]
        
        return domain
    except Exception:
        return None


def clean_text(text: str) -> str:
    """Clean text for processing."""
    if not text:
        return ""
    
    # Remove extra whitespace
    cleaned = " ".join(text.split())
    
    # Remove common unwanted characters
    cleaned = cleaned.replace("\n", " ").replace("\r", " ").replace("\t", " ")
    
    return cleaned.strip()


def chunk_list(items: List[Any], chunk_size: int) -> List[List[Any]]:
    """Split a list into chunks of specified size."""
    return [items[i:i + chunk_size] for i in range(0, len(items), chunk_size)]


def safe_get(data: Dict[str, Any], path: str, default: Any = None) -> Any:
    """Safely get nested dictionary value using dot notation."""
    try:
        keys = path.split(".")
        value = data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    except Exception:
        return default


def merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any], priority: str = "dict2") -> Dict[str, Any]:
    """
    Merge two dictionaries with conflict resolution.
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary
        priority: Which dict takes priority for conflicts ("dict1" or "dict2")
    
    Returns:
        Merged dictionary
    """
    if priority == "dict1":
        result = dict2.copy()
        result.update(dict1)
    else:
        result = dict1.copy()
        result.update(dict2)
    
    return result


def calculate_completeness_score(data: Dict[str, Any], required_fields: List[str]) -> float:
    """Calculate data completeness score based on required fields."""
    if not required_fields:
        return 1.0
    
    filled_fields = sum(1 for field in required_fields if data.get(field) is not None)
    return filled_fields / len(required_fields)


def format_currency(amount: Optional[Union[int, float]], currency: str = "USD") -> Optional[str]:
    """Format currency amount."""
    if amount is None:
        return None
    
    try:
        if currency == "USD":
            return f"${amount:,.2f}"
        else:
            return f"{amount:,.2f} {currency}"
    except Exception:
        return str(amount)


def parse_employee_count(count_str: str) -> Optional[int]:
    """Parse employee count from various string formats."""
    if not count_str:
        return None
    
    try:
        # Handle ranges like "10-50", "100+"
        count_str = count_str.lower().strip()
        
        if "+" in count_str:
            # Handle "100+" format
            return int(count_str.replace("+", ""))
        elif "-" in count_str:
            # Handle "10-50" format - return midpoint
            parts = count_str.split("-")
            if len(parts) == 2:
                low = int(parts[0].strip())
                high = int(parts[1].strip())
                return (low + high) // 2
        else:
            # Direct number
            return int(count_str)
    except Exception:
        return None


def validate_email(email: str) -> bool:
    """Validate email address format."""
    if not email:
        return False
    
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_url(url: str) -> bool:
    """Validate URL format."""
    if not url:
        return False
    
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def get_current_timestamp() -> int:
    """Get current timestamp in seconds."""
    return int(datetime.now(timezone.utc).timestamp())


def timestamp_to_datetime(timestamp: Union[int, float]) -> datetime:
    """Convert timestamp to datetime object."""
    return datetime.fromtimestamp(timestamp, tz=timezone.utc)


def datetime_to_timestamp(dt: datetime) -> int:
    """Convert datetime to timestamp."""
    return int(dt.timestamp())


class RateLimiter:
    """Simple rate limiter for API calls."""
    
    def __init__(self, max_calls: int, time_window: int = 60):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    def can_make_call(self) -> bool:
        """Check if a call can be made within rate limits."""
        now = get_current_timestamp()
        
        # Remove old calls outside the time window
        self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
        
        # Check if we can make another call
        return len(self.calls) < self.max_calls
    
    def record_call(self) -> None:
        """Record a call."""
        self.calls.append(get_current_timestamp())


def retry_with_backoff(max_retries: int = 3, backoff_factor: float = 2.0):
    """Decorator for retrying functions with exponential backoff."""
    import asyncio
    import functools

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e

                    if attempt < max_retries:
                        wait_time = backoff_factor ** attempt
                        await asyncio.sleep(wait_time)
                    else:
                        raise last_exception

            raise last_exception

        return wrapper
    return decorator
