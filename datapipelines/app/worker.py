"""
TractionX Data Pipeline Service - RQ Worker

This module implements the worker process that handles background tasks
using Redis Queue (RQ) for job processing.
"""

import rq
from redis import Redis

import sys
import signal

from app.configs import settings, get_logger

# Configure logging
logger = get_logger(__name__)


# Task functions are imported from app.tasks module


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}, shutting down worker...")
    sys.exit(0)


if __name__ == "__main__":
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info("Starting TractionX Data Pipeline Worker")
    logger.info(f"Redis URL: {settings.REDIS_HOST}")
    logger.info(f"Environment: {settings.ENVIRONMENT}")

    try:
        # Create Redis connection
        redis_conn = Redis.from_url(settings.redis_connection_string, decode_responses=True)

        # Test Redis connection
        redis_conn.ping()
        logger.info("Redis connection successful")

        # Create RQ worker
        worker = rq.Worker(['default'], connection=redis_conn)

        logger.info("Pipeline worker initialized")
        logger.info("Available queues: default")
        logger.info("Listening for jobs...")

        # Start worker
        worker.work()

    except KeyboardInterrupt:
        logger.info("Worker interrupted by user")
    except Exception as e:
        logger.error(f"Worker failed to start: {e}")
        sys.exit(1)
    finally:
        logger.info("Worker shutdown complete")
