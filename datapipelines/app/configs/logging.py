"""
Logging configuration for TractionX Data Pipeline Service.
"""

import logging
import sys
import json
from typing import Any, Dict

import structlog
from structlog.stdlib import ProcessorFormatter

from .settings import settings


class ReadableJSONRenderer:
    """Custom JSON renderer that produces more readable output."""

    def __init__(self, indent: int = 2):
        self.indent = indent

    def __call__(self, logger: logging.Logger, method_name: str, event_dict: Dict[str, Any]) -> str:
        # Extract the actual event if it's a JSON string
        if isinstance(event_dict.get("event"), str):
            try:
                # Try to parse if it's a JSON string
                event = json.loads(event_dict["event"])
                if isinstance(event, dict) and "event" in event:
                    # If it's a nested JSON, use the inner event
                    event_dict["event"] = event["event"]
                    # Merge other fields if they exist
                    for key, value in event.items():
                        if key != "event" and key not in event_dict:
                            event_dict[key] = value
            except json.JSONDecodeError:
                # If it's not JSON, keep it as is
                pass

        # Remove redundant logger name if it's the same as the parent
        if "logger" in event_dict and event_dict["logger"].startswith("datapipelines"):
            event_dict["logger"] = event_dict["logger"].replace("datapipelines.", "")

        # Format the output
        return json.dumps(event_dict, indent=self.indent, ensure_ascii=False)


def configure_logging() -> None:
    """Configure structured logging for the application."""
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL),
    )

    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            ReadableJSONRenderer(),  # Use our custom renderer
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Configure root logger
    root_logger = logging.getLogger()
    handler = logging.StreamHandler()
    handler.setFormatter(
        ProcessorFormatter(
            processor=ReadableJSONRenderer(),  # Use our custom renderer here too
            foreign_pre_chain=[
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.processors.TimeStamper(fmt="iso"),
            ],
        )
    )
    root_logger.addHandler(handler)

    # Set log levels for external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("boto3").setLevel(logging.WARNING)
    logging.getLogger("botocore").setLevel(logging.WARNING)
    logging.getLogger("rq.worker").setLevel(logging.INFO)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


class PipelineLogger:
    """Enhanced logger for pipeline operations with context tracking."""

    def __init__(self, pipeline_name: str, job_id: str = None):
        self.logger = get_logger(f"datapipelines.pipeline.{pipeline_name}")
        self.pipeline_name = pipeline_name
        self.job_id = job_id
        self.context = {
            "pipeline": pipeline_name,
            "job_id": job_id
        }

    def bind(self, **kwargs) -> "PipelineLogger":
        """Add context to logger."""
        new_context = {**self.context, **kwargs}
        new_logger = PipelineLogger(self.pipeline_name, self.job_id)
        new_logger.context = new_context
        new_logger.logger = self.logger.bind(**kwargs)
        return new_logger

    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(message, **{**self.context, **kwargs})

    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self.logger.info(message, **{**self.context, **kwargs})

    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(message, **{**self.context, **kwargs})

    def error(self, message: str, **kwargs) -> None:
        """Log error message."""
        self.logger.error(message, **{**self.context, **kwargs})

    def critical(self, message: str, **kwargs) -> None:
        """Log critical message."""
        self.logger.critical(message, **{**self.context, **kwargs})

    def log_pipeline_start(self, input_data: Dict[str, Any]) -> None:
        """Log pipeline start."""
        self.info(
            "Pipeline started",
            input_data_keys=list(input_data.keys()),
            input_size=len(str(input_data))
        )

    def log_pipeline_success(self, output_data: Dict[str, Any], duration: float) -> None:
        """Log pipeline success."""
        self.info(
            "Pipeline completed successfully",
            output_data_keys=list(output_data.keys()),
            output_size=len(str(output_data)),
            duration_seconds=duration
        )

    def log_pipeline_error(self, error: Exception, duration: float) -> None:
        """Log pipeline error."""
        self.error(
            "Pipeline failed",
            error_type=type(error).__name__,
            error_message=str(error),
            duration_seconds=duration,
            exc_info=True
        )

    def log_api_call(self, service: str, endpoint: str, status_code: int, duration: float) -> None:
        """Log external API call."""
        self.info(
            "External API call",
            service=service,
            endpoint=endpoint,
            status_code=status_code,
            duration_seconds=duration
        )

    def log_storage_operation(self, operation: str, storage_type: str, key: str, success: bool) -> None:
        """Log storage operation."""
        level = "info" if success else "error"
        getattr(self, level)(
            f"Storage {operation}",
            storage_type=storage_type,
            key=key,
            success=success
        )
