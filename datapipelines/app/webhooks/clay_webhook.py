"""
Clay webhook endpoint for TractionX Data Pipeline Service.
"""

import hmac
import hashlib
from typing import Dict, Any
from fastapi import API<PERSON>outer, Request, HTTPException, Header
from fastapi.responses import JSONResponse

from app.configs import get_logger, settings
from app.utils import QueueClient


logger = get_logger(__name__)
router = APIRouter(prefix="")


def verify_clay_signature(payload: bytes, signature: str, secret: str) -> bool:
    """Verify Clay webhook signature."""
    if not secret:
        logger.warning("Clay webhook secret not configured, skipping signature verification")
        return True

    try:
        # <PERSON> typically sends signature as 'sha256=<hash>'
        if signature.startswith("sha256="):
            signature = signature[7:]

        # Calculate expected signature
        expected_signature = hmac.new(secret.encode("utf-8"), payload, hashlib.sha256).hexdigest()

        # Compare signatures
        return hmac.compare_digest(signature, expected_signature)

    except Exception as e:
        logger.error(f"Error verifying Clay signature: {e}")
        return False


@router.post("/enrichment")
async def clay_enrichment_webhook(
    request: Request,
    # tx_clay_signature: str = Header(None, alias="tx-auth")
) -> JSONResponse:
    """
    Handle Clay enrichment webhook.

    This endpoint receives enrichment data from Clay and triggers
    the appropriate pipeline jobs.
    """
    try:
        # Get request body
        body = await request.body()

        # Verify signature if configure

        # Parse JSON payload
        try:
            payload = await request.json()
        except Exception as e:
            logger.error(f"Failed to parse Clay webhook payload: {e}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")

        logger.info("Received Clay webhook", payload_keys=list(payload.keys()))

        # Extract relevant data from Clay payload
        enrichment_data = payload

        if not enrichment_data:
            logger.warning("No valid enrichment data found in Clay webhook")
            return JSONResponse(status_code=200, content={"status": "ignored", "message": "No actionable data found"})

        # Queue enrichment job
        job_result = await queue_clay_enrichment_job(enrichment_data)

        logger.info(
            "Clay enrichment job queued", job_id=job_result.get("job_id"), company_id=enrichment_data.get("company_id")
        )

        return JSONResponse(
            status_code=200,
            content={"status": "success", "message": "Enrichment job queued", "job_id": job_result.get("job_id")},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Clay webhook error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


async def queue_clay_enrichment_job(enrichment_data: Dict[str, Any]) -> Dict[str, Any]:
    """Queue a Clay enrichment job."""
    try:
        # Use our custom queue client
        queue_client = QueueClient()

        # Prepare job data
        job_data = {
            "company_id": enrichment_data["meta"].get("company_id", ""),
            "org_id": enrichment_data["meta"].get("org_id", ""),
            "company_name": enrichment_data["meta"].get("name", ""),
            "domain": enrichment_data["meta"].get("domain"),
            "clay_data": enrichment_data,
            "webhook_source": "clay",
        }

        # Queue the job
        job_id = await queue_client.enqueue_job(
            queue_name="default",
            job_function="tasks.company_enrichment.enrich_company_data_sync",
            job_data=job_data,
            timeout=settings.PIPELINE_TIMEOUT,
        )

        return {"job_id": job_id, "status": "queued"}

    except Exception as e:
        logger.error(f"Failed to queue Clay enrichment job: {e}")
        raise


@router.get("/status")
async def clay_webhook_status():
    """Get Clay webhook status."""
    return {
        "status": "active",
        "webhook_type": "clay_enrichment",
        "signature_verification": bool(settings.CLAY_WEBHOOK_SECRET),
    }
