"""
Generic webhook endpoint for TractionX Data Pipeline Service.
"""

from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse

from app.configs import get_logger


logger = get_logger(__name__)
router = APIRouter()


@router.post("/trigger")
async def generic_pipeline_trigger(request: Request) -> JSONResponse:
    """
    Generic webhook endpoint for triggering pipelines.
    
    This endpoint can be used by the main backend or other services
    to trigger data pipeline jobs.
    """
    try:
        # Parse JSON payload
        payload = await request.json()
        
        logger.info("Received generic pipeline trigger", payload_keys=list(payload.keys()))
        
        # Extract required fields
        company_id = payload.get("company_id")
        org_id = payload.get("org_id")
        company_name = payload.get("company_name")
        
        if not all([company_id, org_id, company_name]):
            raise HTTPException(
                status_code=400,
                detail="Missing required fields: company_id, org_id, company_name"
            )
        
        # TODO: Queue pipeline jobs based on payload
        # This is a placeholder implementation
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": "Pipeline trigger received",
                "company_id": company_id
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Generic webhook error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/status")
async def generic_webhook_status():
    """Get generic webhook status."""
    return {
        "status": "active",
        "webhook_type": "generic_trigger"
    }
