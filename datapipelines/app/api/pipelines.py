"""
Pipeline API endpoints for TractionX Data Pipeline Service.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import redis.asyncio as redis

from app.configs import get_logger, settings
from app.models.base import PipelineStatus
from app.utils import QueueClient


logger = get_logger(__name__)
router = APIRouter()


class PipelineTriggerRequest(BaseModel):
    """Request model for triggering a pipeline."""
    
    company_id: str = Field(..., description="Company identifier")
    org_id: str = Field(..., description="Organization identifier")
    company_name: str = Field(..., description="Company name")
    domain: Optional[str] = Field(None, description="Company domain")
    form_data: Optional[Dict[str, Any]] = Field(None, description="Form submission data")
    pipeline_types: list[str] = Field(
        default=["company", "founder", "news", "embedding"],
        description="Types of pipelines to run"
    )
    priority: str = Field(default="normal", description="Job priority (low, normal, high)")


class PipelineStatusResponse(BaseModel):
    """Response model for pipeline status."""
    
    job_id: str
    status: str
    pipeline_type: str
    created_at: Optional[str] = None
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: Optional[float] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


@router.post("/trigger")
async def trigger_pipeline(
    request: PipelineTriggerRequest,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    Trigger a complete data pipeline for a company.
    
    This endpoint triggers all specified pipeline types for enriching
    company data from various sources.
    """
    try:
        logger.info(
            "Triggering pipeline",
            company_id=request.company_id,
            company_name=request.company_name,
            pipeline_types=request.pipeline_types
        )
        
        # Determine queue based on priority
        queue_name = {
            "low": "low_priority",
            "normal": "default", 
            "high": "high_priority"
        }.get(request.priority, "default")
        
        # Create queue client
        queue_client = QueueClient()
        
        # Queue jobs for each pipeline type
        job_ids = {}
        
        for pipeline_type in request.pipeline_types:
            job_data = {
                "company_id": request.company_id,
                "org_id": request.org_id,
                "company_name": request.company_name,
                "domain": request.domain,
                "form_data": request.form_data,
                "pipeline_type": pipeline_type
            }
            
            # Queue appropriate job based on pipeline type
            if pipeline_type == "company":
                job_id = await queue_client.enqueue_job(
                    queue_name=queue_name,
                    job_function="tasks.company_enrichment.enrich_company_data_sync",
                    job_data=job_data,
                    timeout=settings.PIPELINE_TIMEOUT
                )
            elif pipeline_type == "founder":
                job_id = await queue_client.enqueue_job(
                    queue_name=queue_name,
                    job_function="tasks.founder_enrichment.enrich_founder_data_sync",
                    job_data=job_data,
                    timeout=settings.PIPELINE_TIMEOUT
                )
            elif pipeline_type == "news":
                job_id = await queue_client.enqueue_job(
                    queue_name=queue_name,
                    job_function="tasks.news_aggregation.aggregate_news_data_sync",
                    job_data=job_data,
                    timeout=settings.PIPELINE_TIMEOUT
                )
            elif pipeline_type == "embedding":
                job_id = await queue_client.enqueue_job(
                    queue_name=queue_name,
                    job_function="tasks.embedding_generation.generate_embeddings_sync",
                    job_data=job_data,
                    timeout=settings.PIPELINE_TIMEOUT
                )
            else:
                logger.warning(f"Unknown pipeline type: {pipeline_type}")
                continue
            
            job_ids[pipeline_type] = job_id
            
            logger.info(
                "Queued pipeline job",
                pipeline_type=pipeline_type,
                job_id=job_id,
                company_id=request.company_id
            )
        
        await queue_client.close()
        
        return {
            "success": True,
            "message": "Pipeline jobs queued successfully",
            "company_id": request.company_id,
            "job_ids": job_ids,
            "total_jobs": len(job_ids)
        }
    
    except Exception as e:
        logger.error(f"Failed to trigger pipeline: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{job_id}")
async def get_pipeline_status(job_id: str) -> PipelineStatusResponse:
    """
    Get the status of a specific pipeline job.
    """
    try:
        # Create queue client
        queue_client = QueueClient()
        
        # Get job status
        job_data = await queue_client.get_job_status(job_id)
        
        if job_data.get("status") == "not_found":
            raise HTTPException(status_code=404, detail="Job not found")
        
        await queue_client.close()
        
        # Extract pipeline type from job data
        pipeline_type = "unknown"
        if "data" in job_data and isinstance(job_data["data"], dict):
            pipeline_type = job_data["data"].get("pipeline_type", "unknown")
        
        # Convert timestamps to ISO format
        created_at = None
        if "created_at" in job_data:
            try:
                from datetime import datetime
                created_at = datetime.fromtimestamp(float(job_data["created_at"])).isoformat()
            except:
                pass
        
        started_at = None
        if "started_at" in job_data:
            try:
                from datetime import datetime
                started_at = datetime.fromtimestamp(float(job_data["started_at"])).isoformat()
            except:
                pass
        
        completed_at = None
        if "completed_at" in job_data:
            try:
                from datetime import datetime
                completed_at = datetime.fromtimestamp(float(job_data["completed_at"])).isoformat()
            except:
                pass
        
        response = PipelineStatusResponse(
            job_id=job_id,
            status=job_data.get("status", "unknown"),
            pipeline_type=pipeline_type,
            created_at=created_at,
            started_at=started_at,
            completed_at=completed_at,
            error_message=job_data.get("error"),
            result=job_data.get("result")
        )
        
        return response
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats")
async def get_pipeline_stats() -> Dict[str, Any]:
    """
    Get pipeline statistics and queue information.
    """
    try:
        # Create queue client
        queue_client = QueueClient()
        
        # Get queue statistics
        queues = ["default", "high_priority", "low_priority"]
        queue_stats = {}
        
        for queue_name in queues:
            stats = await queue_client.get_queue_stats(queue_name)
            queue_stats[queue_name] = stats
        
        await queue_client.close()
        
        # Worker stats would need to be implemented separately
        # For now, return basic info
        worker_stats = {
            "total_workers": 1,  # This would need to be tracked separately
            "active_workers": 0,  # This would need to be tracked separately
            "idle_workers": 1   # This would need to be tracked separately
        }
        
        return {
            "queues": queue_stats,
            "workers": worker_stats,
            "service_info": {
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT
            }
        }
    
    except Exception as e:
        logger.error(f"Failed to get pipeline stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/jobs/{job_id}")
async def cancel_pipeline_job(job_id: str) -> Dict[str, Any]:
    """
    Cancel a pipeline job.
    """
    try:
        # Create queue client
        queue_client = QueueClient()
        
        # Cancel the job
        success = await queue_client.cancel_job(job_id)
        
        await queue_client.close()
        
        if not success:
            raise HTTPException(status_code=404, detail="Job not found or could not be cancelled")
        
        return {
            "success": True,
            "message": f"Job {job_id} has been cancelled",
            "job_id": job_id
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
