"""
RQ Tasks for TractionX Data Pipeline Service.
"""

from typing import Dict, Callable, Any
from .company_enrichment import enrich_company_data, enrich_company_data_sync
from .founder_enrichment import enrich_founder_data, enrich_founder_data_sync
from .news_aggregation import aggregate_news_data, aggregate_news_data_sync
from .embedding_generation import generate_embeddings, generate_embeddings_sync
from .etl_merge import merge_enrichment_data, merge_enrichment_data_sync


def get_job_handlers() -> Dict[str, Callable]:
    """Get all available job handlers."""
    return {
        "enrich_company_data": enrich_company_data_sync,
        "enrich_founder_data": enrich_founder_data_sync,
        "aggregate_news_data": aggregate_news_data_sync,
        "generate_embeddings": generate_embeddings_sync,
        "merge_enrichment_data": merge_enrichment_data_sync,
    }


# Export task functions for direct import
__all__ = [
    "enrich_company_data",
    "enrich_founder_data", 
    "aggregate_news_data",
    "generate_embeddings",
    "merge_enrichment_data",
    "get_job_handlers"
]
