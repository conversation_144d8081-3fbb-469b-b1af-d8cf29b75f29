"""
Company enrichment task for TractionX Data Pipeline Service.
"""

import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

from app.configs import get_logger, settings
from app.models.company import CompanyData, CompanyEnrichmentData, ClayCompanyData
from app.models.base import PipelineStatus, EnrichmentSource
from app.pipelines.company import CompanyEnrichmentPipeline
from app.storage.rds_storage import RDSStorage


logger = get_logger(__name__)


async def enrich_company_data(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enrich company data using external APIs (Clay).
    
    Args:
        job_data: Dictionary containing:
            - company_id: Unique company identifier
            - org_id: Organization identifier
            - company_name: Company name for enrichment
            - domain: Company domain (optional)
            - form_data: Original form submission data (optional)
    
    Returns:
        Dictionary with enrichment results
    """
    job_id = job_data.get("job_id", "unknown")
    company_id = job_data.get("company_id")
    org_id = job_data.get("org_id")
    company_name = job_data.get("company_name")
    domain = job_data.get("domain")
    form_data = job_data.get("form_data")
    
    logger.info(
        "Starting company enrichment",
        job_id=job_id,
        company_id=company_id,
        company_name=company_name
    )
    
    if not all([company_id, org_id, company_name]):
        error_msg = "Missing required fields: company_id, org_id, company_name"
        logger.error(error_msg, job_id=job_id)
        return {
            "success": False,
            "error": error_msg,
            "job_id": job_id
        }
    
    try:
        # Initialize storage
        storage = RDSStorage()
        await storage.initialize()
        
        # Initialize pipeline
        pipeline = CompanyEnrichmentPipeline()
        await pipeline.initialize()
        
        # Prepare input data
        input_data = {
            "company_id": company_id,
            "org_id": org_id,
            "company_name": company_name,
            "domain": domain,
            "form_data": form_data
        }
        
        # Run enrichment pipeline
        result = await pipeline.process(input_data)
        
        if result.success:
            # Store enriched data
            enrichment_data = CompanyEnrichmentData(
                company_id=company_id,
                org_id=org_id,
                clay_data=result.data.get("clay_data"),
                enrichment_status={"clay": "success"},
                last_enriched=datetime.utcnow()
            )
            
            # Store in database
            await storage.upsert(
                "companies",
                {
                    "company_id": company_id,
                    "org_id": org_id,
                    "name": company_name,
                    "data": enrichment_data.model_dump(),
                    "source": EnrichmentSource.CLAY.value
                },
                ["company_id"]
            )
            
            logger.info(
                "Company enrichment completed successfully",
                job_id=job_id,
                company_id=company_id
            )
            
            return {
                "success": True,
                "job_id": job_id,
                "company_id": company_id,
                "enrichment_data": enrichment_data.model_dump(),
                "processing_time": result.processing_time
            }
        
        else:
            # Handle enrichment failure
            error_msg = result.error_message or "Unknown enrichment error"
            logger.error(
                "Company enrichment failed",
                job_id=job_id,
                company_id=company_id,
                error=error_msg
            )
            
            # Store failure status
            enrichment_data = CompanyEnrichmentData(
                company_id=company_id,
                org_id=org_id,
                enrichment_status={"clay": "failed"},
                last_enriched=datetime.utcnow()
            )
            
            await storage.upsert(
                "companies",
                {
                    "company_id": company_id,
                    "org_id": org_id,
                    "name": company_name,
                    "data": enrichment_data.model_dump(),
                    "source": EnrichmentSource.FORM_SUBMISSION.value
                },
                ["company_id"]
            )
            
            return {
                "success": False,
                "job_id": job_id,
                "company_id": company_id,
                "error": error_msg,
                "processing_time": result.processing_time
            }
    
    except Exception as e:
        error_msg = f"Company enrichment task failed: {str(e)}"
        logger.error(
            error_msg,
            job_id=job_id,
            company_id=company_id,
            exc_info=True
        )
        
        return {
            "success": False,
            "job_id": job_id,
            "company_id": company_id,
            "error": error_msg
        }
    
    finally:
        # Cleanup resources
        try:
            if 'storage' in locals():
                await storage.cleanup()
            if 'pipeline' in locals():
                await pipeline.cleanup()
        except Exception as e:
            logger.warning(f"Error during cleanup: {e}")


def enrich_company_data_sync(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous wrapper for the async company enrichment task.
    This is needed for RQ compatibility.
    """
    return asyncio.run(enrich_company_data(job_data))
