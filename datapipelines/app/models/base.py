"""
Base models and enums for TractionX Data Pipeline Service.
"""

from enum import Enum
from typing import Any, Dict, Optional
from datetime import datetime, timezone
from pydantic import BaseModel, Field


class TractionXModel(BaseModel):
    """Base model for TractionX pipeline models."""
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# Export BaseModel for compatibility
__all__ = ["BaseModel", "TractionXModel", "PipelineStatus", "EnrichmentSource", "ProcessingResult", "PipelineJobMetadata"]


class PipelineStatus(str, Enum):
    """Status of a pipeline job."""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class EnrichmentSource(str, Enum):
    """Source of enrichment data."""
    FORM_SUBMISSION = "form_submission"
    CLAY = "clay"
    PDL = "pdl"
    BING_NEWS = "bing_news"
    OPENAI = "openai"
    MANUAL = "manual"


class ProcessingResult(BaseModel):
    """Result of a pipeline processing operation."""
    
    success: bool = Field(..., description="Whether processing was successful")
    data: Optional[Dict[str, Any]] = Field(None, description="Processed data")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Processing metadata")
    
    # Error information
    error_type: Optional[str] = Field(None, description="Type of error if failed")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    
    # Processing statistics
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    records_processed: Optional[int] = Field(None, description="Number of records processed")
    records_failed: Optional[int] = Field(None, description="Number of records that failed")
    
    # Source tracking
    source: Optional[EnrichmentSource] = Field(None, description="Source of the data")
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Source-specific metadata")

    @classmethod
    def success_result(
        cls,
        data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> "ProcessingResult":
        """Create a successful result."""
        return cls(
            success=True,
            data=data,
            metadata=metadata or {},
            **kwargs
        )

    @classmethod
    def error_result(
        cls,
        error_message: str,
        error_type: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> "ProcessingResult":
        """Create an error result."""
        return cls(
            success=False,
            error_message=error_message,
            error_type=error_type,
            error_details=error_details,
            **kwargs
        )


class PipelineJobMetadata(BaseModel):
    """Metadata for pipeline jobs."""
    
    job_id: str = Field(..., description="Unique job identifier")
    pipeline_name: str = Field(..., description="Name of the pipeline")
    status: PipelineStatus = Field(default=PipelineStatus.PENDING, description="Current job status")
    
    # Timing
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(None, description="When job started processing")
    completed_at: Optional[datetime] = Field(None, description="When job completed")
    
    # Progress tracking
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="Job progress (0.0 to 1.0)")
    current_step: Optional[str] = Field(None, description="Current processing step")
    total_steps: Optional[int] = Field(None, description="Total number of steps")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if failed")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    
    # Context
    org_id: Optional[str] = Field(None, description="Organization ID")
    deal_id: Optional[str] = Field(None, description="Company ID")
    company_id: Optional[str] = Field(None, description="Company ID")
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    @property
    def duration(self) -> Optional[float]:
        """Calculate duration in seconds if job has started."""
        if not self.started_at:
            return None
        end_time = self.completed_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds()

    @property
    def is_completed(self) -> bool:
        """Check if job is in a completed state."""
        return self.status in [PipelineStatus.SUCCESS, PipelineStatus.FAILED, PipelineStatus.CANCELLED]

    @property
    def is_running(self) -> bool:
        """Check if job is currently running."""
        return self.status in [PipelineStatus.RUNNING, PipelineStatus.RETRYING] 