"""
Founder data models for TractionX Data Pipeline Service.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from pydantic import Field, HttpUrl

from .base import BaseModel, EnrichmentSource


class FounderData(BaseModel):
    """Core founder data structure."""
    
    # Core identifiers
    founder_id: str = Field(..., description="Unique founder identifier")
    company_id: str = Field(..., description="Associated company ID")
    
    # Personal information
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    full_name: Optional[str] = Field(None, description="Full name")
    email: Optional[str] = Field(None, description="Email address")
    
    # Professional information
    title: Optional[str] = Field(None, description="Current title/position")
    bio: Optional[str] = Field(None, description="Professional biography")
    
    # Location
    location: Optional[str] = Field(None, description="Current location")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")
    
    # Social profiles
    linkedin_url: Optional[HttpUrl] = Field(None, description="LinkedIn profile URL")
    twitter_url: Optional[HttpUrl] = Field(None, description="Twitter profile URL")
    github_url: Optional[HttpUrl] = Field(None, description="GitHub profile URL")
    
    # Professional background
    previous_companies: List[str] = Field(default_factory=list, description="Previous companies")
    education: List[Dict[str, Any]] = Field(default_factory=list, description="Education background")
    skills: List[str] = Field(default_factory=list, description="Professional skills")
    
    # Metadata
    source: EnrichmentSource = Field(..., description="Data source")
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Data confidence")
    
    # Additional data
    additional_data: Dict[str, Any] = Field(default_factory=dict, description="Additional founder data")


class PDLFounderData(BaseModel):
    """Founder data from People Data Labs (PDL) enrichment service."""
    
    # PDL-specific fields
    pdl_id: Optional[str] = Field(None, description="PDL person ID")
    
    # Personal info
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    full_name: Optional[str] = Field(None, description="Full name")
    middle_name: Optional[str] = Field(None, description="Middle name")
    
    # Contact info
    emails: List[str] = Field(default_factory=list, description="Email addresses")
    phone_numbers: List[str] = Field(default_factory=list, description="Phone numbers")
    
    # Location
    location_name: Optional[str] = Field(None, description="Location name")
    location_country: Optional[str] = Field(None, description="Country")
    location_region: Optional[str] = Field(None, description="Region/State")
    location_locality: Optional[str] = Field(None, description="City")
    
    # Professional info
    job_title: Optional[str] = Field(None, description="Current job title")
    job_company_name: Optional[str] = Field(None, description="Current company")
    job_start_date: Optional[datetime] = Field(None, description="Current job start date")
    
    # Work history
    experience: List[Dict[str, Any]] = Field(default_factory=list, description="Work experience")
    education: List[Dict[str, Any]] = Field(default_factory=list, description="Education history")
    
    # Social profiles
    linkedin_url: Optional[str] = Field(None, description="LinkedIn URL")
    twitter_url: Optional[str] = Field(None, description="Twitter URL")
    github_url: Optional[str] = Field(None, description="GitHub URL")
    facebook_url: Optional[str] = Field(None, description="Facebook URL")
    
    # Skills and interests
    skills: List[str] = Field(default_factory=list, description="Professional skills")
    interests: List[str] = Field(default_factory=list, description="Interests")
    
    # Additional PDL data
    pdl_metadata: Dict[str, Any] = Field(default_factory=dict, description="PDL-specific metadata")
    enrichment_date: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_founder_data(self, founder_id: str, company_id: str) -> FounderData:
        """Convert PDL data to standard FounderData format."""
        # Get primary email
        primary_email = self.emails[0] if self.emails else None
        
        # Build location string
        location_parts = [
            self.location_locality,
            self.location_region,
            self.location_country
        ]
        location = ", ".join([part for part in location_parts if part])
        
        return FounderData(
            founder_id=founder_id,
            company_id=company_id,
            first_name=self.first_name,
            last_name=self.last_name,
            full_name=self.full_name,
            email=primary_email,
            title=self.job_title,
            location=location or None,
            country=self.location_country,
            city=self.location_locality,
            linkedin_url=self.linkedin_url,
            twitter_url=self.twitter_url,
            github_url=self.github_url,
            previous_companies=[
                exp.get("company", {}).get("name", "")
                for exp in self.experience
                if exp.get("company", {}).get("name")
            ],
            education=self.education,
            skills=self.skills,
            source=EnrichmentSource.PDL,
            additional_data={
                "pdl_id": self.pdl_id,
                "middle_name": self.middle_name,
                "emails": self.emails,
                "phone_numbers": self.phone_numbers,
                "location_region": self.location_region,
                "job_company_name": self.job_company_name,
                "job_start_date": self.job_start_date.isoformat() if self.job_start_date else None,
                "experience": self.experience,
                "facebook_url": self.facebook_url,
                "interests": self.interests,
                "pdl_metadata": self.pdl_metadata,
                "enrichment_date": self.enrichment_date.isoformat()
            }
        )


class FounderEnrichmentData(BaseModel):
    """Enriched founder data from multiple sources."""
    
    # Core founder info
    founder_id: str = Field(..., description="Unique founder identifier")
    company_id: str = Field(..., description="Associated company ID")
    org_id: str = Field(..., description="Organization ID")
    
    # Form submission data (highest priority)
    form_data: Optional[FounderData] = Field(None, description="Data from form submission")
    
    # Enrichment data
    pdl_data: Optional[PDLFounderData] = Field(None, description="PDL enrichment data")
    
    # Processing metadata
    enrichment_status: Dict[str, str] = Field(default_factory=dict, description="Status of each enrichment")
    last_enriched: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Quality metrics
    completeness_score: Optional[float] = Field(None, description="Data completeness score")
    confidence_score: Optional[float] = Field(None, description="Overall confidence score")
    
    def get_canonical_data(self) -> FounderData:
        """Get canonical founder data using merge rules (form > pdl)."""
        # Start with form data if available
        if self.form_data:
            canonical = self.form_data.model_copy()
        else:
            # Create base structure
            canonical = FounderData(
                founder_id=self.founder_id,
                company_id=self.company_id,
                source=EnrichmentSource.FORM_SUBMISSION
            )
        
        # Merge PDL data for missing fields
        if self.pdl_data:
            pdl_founder = self.pdl_data.to_founder_data(self.founder_id, self.company_id)
            
            # Fill missing fields with PDL data
            for field_name, field_value in pdl_founder.model_dump().items():
                if field_name in ["founder_id", "company_id", "source", "last_updated"]:
                    continue
                    
                current_value = getattr(canonical, field_name, None)
                if current_value is None and field_value is not None:
                    setattr(canonical, field_name, field_value)
        
        return canonical
