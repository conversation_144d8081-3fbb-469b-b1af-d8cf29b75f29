"""
Embedding data models for TractionX Data Pipeline Service.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from pydantic import Field

from .base import BaseModel, EnrichmentSource


class TextEmbedding(BaseModel):
    """Individual text embedding data structure."""
    
    # Core identifiers
    embedding_id: str = Field(..., description="Unique embedding identifier")
    source_id: str = Field(..., description="Source object ID (company, founder, etc.)")
    source_type: str = Field(..., description="Type of source object")
    
    # Text and embedding
    text: str = Field(..., description="Original text that was embedded")
    embedding: List[float] = Field(..., description="Vector embedding")
    
    # Metadata
    field_name: str = Field(..., description="Name of the field that was embedded")
    model_name: str = Field(..., description="Embedding model used")
    model_version: Optional[str] = Field(None, description="Model version")
    
    # Processing info
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    token_count: Optional[int] = Field(None, description="Number of tokens in text")
    
    # Quality metrics
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Embedding confidence")
    
    # Additional data
    additional_data: Dict[str, Any] = Field(default_factory=dict, description="Additional embedding data")


class EmbeddingMetadata(BaseModel):
    """Metadata for embedding operations."""
    
    # Processing info
    total_embeddings: int = Field(default=0, description="Total number of embeddings created")
    successful_embeddings: int = Field(default=0, description="Number of successful embeddings")
    failed_embeddings: int = Field(default=0, description="Number of failed embeddings")
    
    # Model info
    model_name: str = Field(..., description="Embedding model used")
    model_version: Optional[str] = Field(None, description="Model version")
    vector_dimension: int = Field(..., description="Dimension of embedding vectors")
    
    # Processing stats
    total_tokens: int = Field(default=0, description="Total tokens processed")
    processing_time: float = Field(default=0.0, description="Total processing time in seconds")
    
    # API usage
    api_calls: int = Field(default=0, description="Number of API calls made")
    api_cost: Optional[float] = Field(None, description="Estimated API cost")
    
    # Timestamps
    started_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    completed_at: Optional[datetime] = Field(None, description="When processing completed")
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of embeddings."""
        if self.total_embeddings == 0:
            return 0.0
        return self.successful_embeddings / self.total_embeddings
    
    @property
    def average_processing_time(self) -> float:
        """Calculate average processing time per embedding."""
        if self.successful_embeddings == 0:
            return 0.0
        return self.processing_time / self.successful_embeddings


class EmbeddingData(BaseModel):
    """Aggregated embedding data for a company/object."""
    
    # Core identifiers
    object_id: str = Field(..., description="ID of the object (company, founder, etc.)")
    object_type: str = Field(..., description="Type of object")
    org_id: str = Field(..., description="Organization ID")
    
    # Embeddings
    embeddings: List[TextEmbedding] = Field(default_factory=list, description="Text embeddings")
    
    # Processing metadata
    metadata: EmbeddingMetadata = Field(..., description="Processing metadata")
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Quality metrics
    completeness_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Embedding completeness")
    
    def add_embedding(self, embedding: TextEmbedding) -> None:
        """Add a new embedding to the collection."""
        self.embeddings.append(embedding)
        self.metadata.total_embeddings += 1
        self.metadata.successful_embeddings += 1
        self.last_updated = datetime.now(timezone.utc)
    
    def get_embeddings_by_field(self, field_name: str) -> List[TextEmbedding]:
        """Get embeddings for a specific field."""
        return [emb for emb in self.embeddings if emb.field_name == field_name]
    
    def get_embedding_by_field(self, field_name: str) -> Optional[TextEmbedding]:
        """Get the first embedding for a specific field."""
        embeddings = self.get_embeddings_by_field(field_name)
        return embeddings[0] if embeddings else None


class EmbeddingEnrichmentData(BaseModel):
    """Enriched embedding data from multiple sources."""
    
    # Core identifiers
    object_id: str = Field(..., description="ID of the object")
    object_type: str = Field(..., description="Type of object")
    org_id: str = Field(..., description="Organization ID")
    
    # Embedding data
    embedding_data: Optional[EmbeddingData] = Field(None, description="Generated embeddings")
    
    # Processing metadata
    enrichment_status: Dict[str, str] = Field(default_factory=dict, description="Status of embedding generation")
    last_enriched: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Configuration
    fields_to_embed: List[str] = Field(default_factory=list, description="Fields that should be embedded")
    embedding_config: Dict[str, Any] = Field(default_factory=dict, description="Embedding configuration")
    
    def get_canonical_data(self) -> Optional[EmbeddingData]:
        """Get canonical embedding data."""
        return self.embedding_data


class EmbeddingBatch(BaseModel):
    """Batch of texts to be embedded together."""
    
    # Batch info
    batch_id: str = Field(..., description="Unique batch identifier")
    texts: List[str] = Field(..., description="Texts to embed")
    
    # Metadata for each text
    text_metadata: List[Dict[str, Any]] = Field(..., description="Metadata for each text")
    
    # Processing config
    model_name: str = Field(..., description="Embedding model to use")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens per text")
    
    # Results
    embeddings: Optional[List[List[float]]] = Field(None, description="Generated embeddings")
    processing_metadata: Optional[EmbeddingMetadata] = Field(None, description="Processing metadata")
    
    @property
    def batch_size(self) -> int:
        """Get the size of the batch."""
        return len(self.texts)
    
    def is_processed(self) -> bool:
        """Check if batch has been processed."""
        return self.embeddings is not None
    
    def get_embedding_for_text(self, text_index: int) -> Optional[List[float]]:
        """Get embedding for a specific text by index."""
        if self.embeddings and 0 <= text_index < len(self.embeddings):
            return self.embeddings[text_index]
        return None
