"""
Context Block Service

Generates and stores rich AI-ready context blocks for deals in S3.
Implements PRD 2: Context Block Generation and S3 Storage.
"""

import json
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.core.config import settings
from app.core.logging import get_logger
from app.models.deal import Deal
from app.models.form import FormWithDetails, Submission
from app.services.base import BaseService
from app.services.context_block.interface import IContextBlockService
from app.utils.aws.s3 import S3Storage
from bson import ObjectId

logger = get_logger(__name__)


class ContextBlockService(BaseService, IContextBlockService):
    """Service for generating and managing AI context blocks."""

    def __init__(self, db=None):
        super().__init__(db)
        self.s3_storage = S3Storage()

    async def initialize(self) -> None:
        """Initialize the service."""
        await self.s3_storage.initialize()

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        if hasattr(self, "s3_storage") and self.s3_storage:
            await self.s3_storage.cleanup()

    async def generate_context_block(
        self,
        deal: Deal,
        submission: Optional[Submission] = None,
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive context block for a deal.

        Args:
            deal: The deal object
            submission: Optional submission data
            form: Optional form data

        Returns:
            Dictionary containing the context block
        """
        try:
            logger.info(f"Generating context block for deal {deal.id}")

            # Get form if not provided
            if deal.form_id:
                # Import here to avoid circular dependency
                from app.services.factory import get_form_service

                form_service = await get_form_service()
                form: FormWithDetails = await form_service.get_form_with_details(  # type: ignore
                    str(deal.form_id)
                )

            # Get submission if not provided
            if not submission and deal.submission_ids:
                # Get the primary submission (first one)
                submission = await Submission.find_one(
                    query={"_id": ObjectId(deal.submission_ids[0])}
                )

            # Build context block
            context_block = {
                "deal_id": str(deal.id),
                "org_id": str(deal.org_id),
                "company_name": deal.company_name,
                "website": deal.company_website,
                "stage": deal.stage,
                "sector": deal.sector,
                "contact_email": deal.invited_email,
                "created_at": deal.created_at,
                "updated_at": deal.updated_at,
                "status": deal.status.value if deal.status else None,
                "form": await self._build_form_context(form, submission),
                "pitch_deck_url": deal.pitch_deck_url,
                "notes": deal.notes,
                "founders": await self._extract_founders_data(deal=deal),
                "ai_metadata": await self._build_ai_metadata(deal),
                "invite_tracking": {
                    "invited_email": deal.invited_email,
                    "invite_status": deal.invite_status,
                    "invite_sent_at": deal.invite_sent_at,
                },
                "generated_at": int(datetime.now(timezone.utc).timestamp()),
                "version": "v1.0",
            }

            # Remove None values for cleaner context
            context_block = self._clean_none_values(context_block)

            logger.info(f"Generated context block for deal {deal.id}")
            return context_block

        except Exception as e:
            logger.error(f"Error generating context block for deal {deal.id}: {str(e)}")
            raise

    async def store_context_block(
        self, deal_id: str, context_block: Dict[str, Any]
    ) -> str:
        """
        Store context block in S3.

        Args:
            deal_id: The deal ID
            context_block: The context block data

        Returns:
            S3 URL of the stored context block
        """
        try:
            # Generate S3 key
            s3_key = f"deals/{deal_id}/context.json"

            # Store in S3 using the correct method
            success = await self.s3_storage.put_object(
                key=s3_key,
                data=context_block,
                metadata={
                    "deal_id": deal_id,
                    "generated_at": str(context_block.get("generated_at")),
                    "version": context_block.get("version", "v1.0"),
                },
            )

            if not success:
                raise Exception("Failed to store context block in S3")

            # Generate S3 URL
            s3_url = f"s3://{settings.S3_BUCKET_SUBMISSIONS}/{s3_key}"

            logger.info(f"Stored context block for deal {deal_id} at {s3_url}")
            return s3_url

        except Exception as e:
            logger.error(f"Error storing context block for deal {deal_id}: {str(e)}")
            raise

    async def get_context_block(self, deal_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve context block from S3.

        Args:
            deal_id: The deal ID

        Returns:
            Context block data or None if not found
        """
        try:
            s3_key = f"deals/{deal_id}/context.json"

            # Retrieve from S3 using the correct method
            content = await self.s3_storage.get_object(s3_key)

            if not content:
                logger.warning(f"Context block not found for deal {deal_id}")
                return None

            # Parse JSON - get_object returns bytes, so decode first
            context_block = json.loads(content.decode("utf-8"))

            logger.info(f"Retrieved context block for deal {deal_id}")
            return context_block

        except Exception as e:
            logger.error(f"Error retrieving context block for deal {deal_id}: {str(e)}")
            return None

    async def update_context_block(
        self, deal: Deal, submission: Optional[Submission] = None
    ) -> Optional[str]:
        """
        Update context block for a deal.

        Args:
            deal: The deal object
            submission: Optional updated submission

        Returns:
            S3 URL of the updated context block
        """
        try:
            # Generate new context block
            context_block = await self.generate_context_block(deal, submission)

            # Store updated context block
            s3_url = await self.store_context_block(str(deal.id), context_block)

            # Update deal with context block URL
            deal.context_block_url = s3_url
            await deal.save(is_update=True)

            return s3_url

        except Exception as e:
            logger.error(f"Error updating context block for deal {deal.id}: {str(e)}")
            return None

    async def _build_form_context(
        self, form: Optional[FormWithDetails], submission: Optional[Submission]
    ) -> Dict[str, Any]:
        """Build form context section."""
        if not form:
            return {}

        form_context = {
            "form_id": str(form.id),
            "form_name": form.name,
            "questions": [],
        }

        if hasattr(form, "sections") and form.sections:
            for section in form.sections:
                if hasattr(section, "questions") and section.questions:
                    for question in section.questions:
                        question_context = {
                            "question_id": str(question.id),
                            "label": question.label,
                            "type": question.type.value
                            if hasattr(question.type, "value")
                            else str(question.type),
                            "answer": None,
                            "raw": {
                                "required": getattr(question, "required", False),
                                "options": getattr(question, "options", None),
                                "help_text": getattr(question, "help_text", None),
                            },
                        }

                        # Add answer if submission provided
                        if submission and submission.answers:
                            answer = submission.answers.get(str(question.id))
                            if answer is not None:
                                question_context["answer"] = answer

                        form_context["questions"].append(question_context)

        return form_context

    async def _extract_founders_data(self, deal: Deal) -> List[Dict[str, Any]]:
        """Extract founders data from submission."""
        return [founder.model_dump() for founder in deal.founders]

    async def _build_ai_metadata(self, deal: Deal) -> Dict[str, Any]:
        """Build AI metadata section."""
        ai_metadata = {
            "enrichment_version": "v1.0",
        }

        # Add scoring data if available
        if deal.scoring:
            ai_metadata.update({
                "score": deal.scoring.get("total_score"),
                "explanation": deal.scoring.get("explanation"),
                "thesis_match": deal.scoring.get("thesis_matches"),
                "market_signal": deal.scoring.get("market_analysis"),
            })  # type: ignore

        # Add exclusion filter results
        if deal.exclusion_filter_result:
            ai_metadata["exclusion_filter"] = deal.exclusion_filter_result  # type: ignore

        return ai_metadata

    def _clean_none_values(self, data: Any) -> Any:
        """Recursively remove None values from data structure."""
        if isinstance(data, dict):
            return {
                k: self._clean_none_values(v) for k, v in data.items() if v is not None
            }
        elif isinstance(data, list):
            return [self._clean_none_values(item) for item in data if item is not None]
        else:
            return data
