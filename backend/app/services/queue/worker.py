"""
Queue worker implementation.

This module provides the worker implementation for processing jobs from the queue.
"""

import asyncio
import importlib
import inspect
import signal
import time
import traceback
from typing import Any, Callable, Dict, List, Optional, Union

from app.core.logging import get_logger
from app.models.queue import Job, QueueType
from app.services.queue.interfaces import QueueServiceInterface
from app.services.queue.worker_interface import JobHandlerInterface, WorkerInterface

logger = get_logger(__name__)


class QueueWorker(WorkerInterface):
    """Worker for processing jobs from the queue."""

    def __init__(
        self,
        queue_service: QueueServiceInterface,
        worker_id: str = "worker-1",
        queue_types: Optional[List[QueueType]] = None,
        job_types: Optional[List[str]] = None,
        concurrency: int = 1,
        poll_interval: float = 5.0,
        shutdown_timeout: int = 60,
    ):
        """
        Initialize a queue worker.

        Args:
            queue_service: Queue service to use
            worker_id: Unique ID for this worker
            queue_types: List of queue types to process (default: [QueueType.DEFAULT])
            job_types: List of job types to process (default: all)
            concurrency: Number of jobs to process concurrently
            poll_interval: How often to check for new jobs when none are available
            shutdown_timeout: How long to wait for jobs to complete during shutdown
        """
        self.queue_service = queue_service
        self.worker_id = worker_id
        self.queue_types = queue_types or [QueueType.DEFAULT]
        self.job_types = job_types
        self.concurrency = concurrency
        self.poll_interval = poll_interval
        self.shutdown_timeout = shutdown_timeout

        self.handlers = {}
        self.running = False
        self.processing_tasks = set()
        self.current_queue_index = 0
        self.main_task = None

    async def initialize(self) -> None:
        """Initialize the worker."""
        # Initialize the queue service
        await self.queue_service.initialize()

    async def cleanup(self) -> None:
        """Clean up resources used by the worker."""
        # Stop the worker if it's running
        if self.running:
            await self.stop()

        # Clean up the queue service
        await self.queue_service.cleanup()

    def register_handler(
        self, job_type: str, handler: Union[JobHandlerInterface, Callable, Any]
    ) -> None:
        """
        Register a handler for a job type.

        Args:
            job_type: Type of job to handle
            handler: Handler function or class
        """
        self.handlers[job_type] = handler

        # add job type to job_types if not already present
        if self.job_types is None:
            self.job_types = [job_type]
        elif job_type not in self.job_types:
            self.job_types.append(job_type)

        logger.info(f"Registered handler for job type: {job_type}")

    def unregister_handler(self, job_type: str) -> bool:
        """
        Unregister a handler for a job type.

        Args:
            job_type: Type of job to unregister handler for

        Returns:
            True if successful, False otherwise
        """
        if job_type in self.handlers:
            del self.handlers[job_type]
            logger.info(f"Unregistered handler for job type: {job_type}")
            return True
        return False

    def register_module(self, module_path: str) -> None:
        """
        Register all handlers from a module.

        The module should have a HANDLERS dictionary mapping job types to handler functions.

        Args:
            module_path: Import path to the module
        """
        try:
            module = importlib.import_module(module_path)
            if hasattr(module, "HANDLERS") and isinstance(module.HANDLERS, dict):
                for job_type, handler in module.HANDLERS.items():
                    self.register_handler(job_type, handler)
                logger.info(f"Registered handlers from module: {module_path}")
            else:
                logger.warning(
                    f"Module {module_path} does not have a HANDLERS dictionary"
                )
        except ImportError as e:
            logger.error(f"Failed to import module {module_path}: {str(e)}")

    def get_registered_handlers(
        self,
    ) -> Dict[str, Union[JobHandlerInterface, Callable, Any]]:
        """Get all registered handlers."""
        return self.handlers.copy()

    def get_queue_service(self) -> QueueServiceInterface:
        """Get the queue service."""
        return self.queue_service

    async def start(self) -> None:
        """Start the worker."""
        if self.running:
            logger.warning("Worker is already running")
            return

        self.running = True
        logger.info(
            f"Starting worker {self.worker_id} with concurrency {self.concurrency}"
        )

        # Set up signal handlers for graceful shutdown
        loop = asyncio.get_running_loop()
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(
                sig, lambda s=sig: asyncio.create_task(self._handle_signal(s))
            )

        # Start worker tasks
        for _ in range(self.concurrency):
            task = asyncio.create_task(self._worker_loop())
            self.processing_tasks.add(task)
            task.add_done_callback(self.processing_tasks.discard)

        # Start scheduled job processor
        scheduled_task = asyncio.create_task(self._process_scheduled_jobs())
        self.processing_tasks.add(scheduled_task)
        scheduled_task.add_done_callback(self.processing_tasks.discard)

        # Start stalled job processor
        stalled_task = asyncio.create_task(self._process_stalled_jobs())
        self.processing_tasks.add(stalled_task)
        stalled_task.add_done_callback(self.processing_tasks.discard)

    async def _handle_signal(self, sig: signal.Signals) -> None:
        """Handle termination signals."""
        logger.info(f"Received signal {sig.name}, shutting down...")
        await self.stop()

    async def stop(self) -> None:
        """Stop the worker."""
        if not self.running:
            logger.warning("Worker is not running")
            return

        logger.info(f"Stopping worker {self.worker_id}")
        self.running = False

        if not self.processing_tasks:
            return

        # Wait for tasks to complete with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*self.processing_tasks, return_exceptions=True),
                timeout=self.shutdown_timeout,
            )
        except asyncio.TimeoutError:
            logger.warning(
                f"Some tasks did not complete within {self.shutdown_timeout} seconds"
            )

        logger.info(f"Worker {self.worker_id} stopped")

    async def _worker_loop(self) -> None:
        """Main worker loop for processing jobs."""
        while self.running:
            try:
                # Round-robin through queue types
                queue_type = self.queue_types[self.current_queue_index]
                self.current_queue_index = (self.current_queue_index + 1) % len(
                    self.queue_types
                )

                # Try to get a job
                result = await self.process_one(
                    queue_type=queue_type, job_types=self.job_types, wait_timeout=0
                )

                if result is None:
                    logger.info(
                        f"No job available, wait before trying again, Queue Type for {queue_type}, Types: {self.job_types} "
                    )
                    # No job available, wait before trying again
                    await asyncio.sleep(self.poll_interval)
            except Exception as e:
                logger.error(f"Error in worker loop: {str(e)}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(self.poll_interval)

    async def _fail_job(
        self,
        job: Job,
        error: Union[str, Exception],
        retry: bool = True,
        error_context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Centralized method to handle job failures.

        Args:
            job: The job that failed
            error: The error message or exception that caused the failure
            retry: Whether the job should be retried
            error_context: Additional context about the failure
        """
        backend = await self.queue_service.get_backend()
        error_message = str(error) if isinstance(error, Exception) else error

        # Log the error with context
        logger.error(
            f"Job {job.id} failed: {error_message}",
            extra={
                "job_id": job.id,
                "job_type": job.type,
                "retry": retry,
                "error_context": error_context or {},
            },
        )

        if isinstance(error, Exception):
            logger.error(traceback.format_exc())

        # Mark job as failed in the backend
        logger.info(f"Marking job {job.id} as failed")
        await backend.fail(job.id, error_message, retry=retry)

    async def process_one(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        job_types: Optional[List[str]] = None,
        wait_timeout: int = 0,
    ) -> Optional[Dict[str, Any]]:
        """Process a single job."""
        # Get a job from the queue
        backend = await self.queue_service.get_backend()
        job = await backend.dequeue(queue_type, job_types, wait_timeout)

        if not job:
            return None

        logger.info(f"Processing job {job.id} of type {job.type}")

        # Get the handler for this job type
        handler = self.handlers.get(job.type)

        if not handler:
            await self._fail_job(
                job,
                f"No handler registered for job type: {job.type}",
                retry=False,
                error_context={"job_type": job.type},
            )
            return None

        # Create a task to process the job
        task = asyncio.create_task(self._process_job(job, handler))
        self.processing_tasks.add(task)
        task.add_done_callback(self.processing_tasks.discard)

        return {"job_id": job.id, "job_type": job.type}

    async def _process_job(
        self, job: Job, handler: Union[JobHandlerInterface, Callable, Any]
    ) -> None:
        """Process a job with the appropriate handler."""
        start_time = time.time()
        backend = await self.queue_service.get_backend()

        try:
            # Get handler instance if it's a factory function
            if callable(handler) and not isinstance(handler, JobHandlerInterface):
                # Check if it's a factory function (returns a handler instance)
                handler_instance = handler()
                if isinstance(handler_instance, JobHandlerInterface):
                    handler = handler_instance

            # Call the handler based on its type
            if isinstance(handler, JobHandlerInterface):
                # Call handle method on JobHandlerInterface
                result = await handler.handle(job)
            elif inspect.iscoroutinefunction(handler):
                # Call async function directly
                result = await handler(job.payload)
            else:
                # Call sync function in executor
                loop = asyncio.get_running_loop()
                result = await loop.run_in_executor(None, handler, job.payload)

            # Mark job as completed
            await backend.complete(job.id, result)

            duration = time.time() - start_time
            logger.info(f"Job {job.id} completed in {duration:.2f}s")

        except Exception as e:
            duration = time.time() - start_time
            await self._fail_job(
                job,
                e,
                retry=True,  # Default to retrying on unexpected errors
                error_context={
                    "duration": duration,
                    "handler_type": type(handler).__name__,
                    "job_type": job.type,
                },
            )

    async def _process_scheduled_jobs(self) -> None:
        """Process scheduled jobs that are due."""
        while self.running:
            try:
                # Process scheduled jobs
                backend = await self.queue_service.get_backend()
                processed = await backend.process_scheduled_jobs()

                if processed > 0:
                    logger.info(f"Processed {processed} scheduled jobs")

                # Wait before checking again
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error processing scheduled jobs: {str(e)}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(5)

    async def _process_stalled_jobs(self) -> None:
        """Check for stalled jobs (jobs that have been processing for too long)."""
        while self.running:
            try:
                # Process stalled jobs
                backend = await self.queue_service.get_backend()
                processed = await backend.process_stalled_jobs()

                if processed > 0:
                    logger.warning(f"Processed {processed} stalled jobs")

                # Wait before checking again
                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Error processing stalled jobs: {str(e)}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(60)
