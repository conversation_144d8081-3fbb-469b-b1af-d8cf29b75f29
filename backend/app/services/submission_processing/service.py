"""
Submission Processing Service Implementation

This module implements the submission processing workflow.
"""

import asyncio
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId
from fastapi import Depends
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.models.deal import Deal
from app.models.queue import JobPriority, QueueType
from app.models.thesis import InvestmentThesis
from app.services.base import BaseService
from app.services.deal.mongo import DealService
from app.services.exclusion_filter.interfaces import (
    ExclusionFilterServiceInterface,
)
from app.services.exclusion_filter.service import ExclusionFilterService
from app.services.factory import (
    get_deal_service,
    get_exclusion_filter_service,
    get_form_service,
    get_queue_service,
    get_thesis_service,
)
from app.services.form.mongo import FormService
from app.services.queue.queue_service import QueueService
from app.services.queue.redis_queue import RedisQueueService
from app.services.submission_processing.interfaces import (
    SubmissionProcessingResult,
    SubmissionProcessingServiceInterface,
)
from app.services.thesis.mongo import ThesisService

logger = get_logger(__name__)


# Dependency injection function following the forms.py pattern


class SubmissionProcessingService(BaseService, SubmissionProcessingServiceInterface):
    """MongoDB implementation of submission processing service."""

    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__(db)
        self.db = db

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        await super().initialize()

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        await super().cleanup()

    async def process_submission(
        self,
        submission_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        answers: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[Union[str, ObjectId]] = None,
    ) -> SubmissionProcessingResult:
        deal_service: DealService = await get_deal_service()  # type: ignore
        """Process a form submission through the complete async workflow."""
        try:
            # Convert IDs to strings for consistency
            submission_id_str = str(submission_id)
            form_id_str = str(form_id)
            org_id_str = str(org_id)
            created_by_str = str(created_by) if created_by else None

            # Create initial deal record
            deal = await self._create_initial_deal(
                submission_id=submission_id_str,
                form_id=form_id_str,
                org_id=org_id_str,
                answers=answers,
                metadata=metadata,
                created_by=created_by_str,
            )

            if not deal:
                logger.error(
                    f"Failed to create initial deal for submission {submission_id_str}"
                )
                return SubmissionProcessingResult(
                    success=False, error="Failed to create initial deal record"
                )

            # Add initial timeline event
            await deal_service.add_timeline_event(
                deal_id=deal.id,
                event="Submission processing started",
                notes=f"Processing submission {submission_id_str}",
            )

            # Launch all processing steps asynchronously
            await self._process_submission_async(
                deal_id=str(deal.id),
                submission_id=submission_id_str,
                form_id=form_id_str,
                org_id=org_id_str,
                answers=answers,
            )

            logger.info(
                f"Successfully initiated async processing for submission {submission_id_str}, created deal {deal.id}"
            )
            return SubmissionProcessingResult(
                success=True,
                deal_id=str(deal.id),
                excluded=False,  # Will be updated by async processing
                matching_theses=[],  # Will be updated by async processing
                enrichment_job_id=None,  # Will be updated by async processing
            )

        except Exception as e:
            logger.error(
                f"Error processing submission {submission_id}: {str(e)}", exc_info=True
            )
            await self.handle_error(
                e,
                {
                    "submission_id": str(submission_id),
                    "form_id": str(form_id),
                    "org_id": str(org_id),
                },
            )
            return SubmissionProcessingResult(success=False, error=str(e))

    async def check_exclusion_filters(
        self,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        answers: Dict[str, Any],
        exclusion_filter_service: ExclusionFilterServiceInterface = Depends(
            get_exclusion_filter_service
        ),
    ) -> Dict[str, Any]:
        """Check if submission should be excluded."""
        try:
            return await exclusion_filter_service.check_exclusion(
                org_id=str(org_id), form_id=str(form_id), answers=answers
            )
        except Exception as e:
            logger.error(f"Error checking exclusion filters: {str(e)}", exc_info=True)
            # Default to not excluded on error
            return {
                "excluded": False,
                "filter_id": None,
                "filter_name": None,
                "reason": f"Error checking exclusion filters: {str(e)}",
            }

    async def find_matching_theses(
        self,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        answers: Dict[str, Any],
    ) -> List[InvestmentThesis]:
        """Find all theses that match the submission."""
        thesis_service: ThesisService = await get_thesis_service()  # type: ignore
        try:
            return await thesis_service.find_matching_theses(
                form_id=form_id, form_responses=answers, org_id=org_id
            )
        except Exception as e:
            logger.error(f"Error finding matching theses: {str(e)}", exc_info=True)
            await self.handle_error(e, {"form_id": str(form_id), "org_id": str(org_id)})
            return []

    async def score_against_theses(
        self,
        theses: List[InvestmentThesis],
        answers: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Score submission against multiple theses."""
        thesis_service: ThesisService = await get_thesis_service()  # type: ignore
        thesis_scores = []

        for thesis in theses:
            try:
                logger.debug(f"Scoring against thesis {thesis.id}")
                score_result = await thesis_service.calculate_score(
                    thesis_id=thesis.id, form_responses=answers
                )

                if score_result and "error" not in score_result:
                    thesis_scores.append({
                        "thesis_id": str(thesis.id),
                        "thesis_name": thesis.name,
                        **score_result,
                    })
                else:
                    logger.warning(
                        f"Failed to score against thesis {thesis.id}: {score_result.get('error', 'Unknown error')}"
                    )

            except Exception as e:
                logger.error(
                    f"Error scoring against thesis {thesis.id}: {str(e)}", exc_info=True
                )
                continue

        return thesis_scores

    async def create_deal_from_processing(
        self,
        submission_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        answers: Dict[str, Any],
        exclusion_result: Dict[str, Any],
        thesis_scores: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[Union[str, ObjectId]] = None,
    ) -> Optional[Deal]:
        """Create a deal record from processing results."""
        form_service: FormService = await get_form_service()  # type: ignore
        deal_service: DealService = await get_deal_service()  # type: ignore
        try:
            # Get form details for core field extraction
            form = await form_service.get_form(str(form_id))
            if not form:
                logger.error(f"Form not found: {form_id}")
                return None

            # Prepare deal data
            deal_data = {}

            # Add exclusion filter result
            deal_data["exclusion_filter_result"] = exclusion_result

            # Add thesis scoring results
            if thesis_scores:
                # Find the highest scoring thesis
                best_score = max(
                    thesis_scores, key=lambda x: x.get("normalized_score", 0)
                )

                deal_data["scoring"] = {
                    "total_score": best_score.get("total_score", 0),
                    "normalized_score": best_score.get("normalized_score", 0),
                    "thesis_matches": [score["thesis_id"] for score in thesis_scores],
                    "best_thesis_id": best_score["thesis_id"],
                    "all_scores": thesis_scores,
                }

            # Add metadata
            if metadata:
                deal_data["metadata"] = metadata

            # Set created_by or default to system user
            created_by_id = created_by or ObjectId("000000000000000000000000")

            # Create the deal using deal service
            deal = await deal_service.create_deal_from_submission(
                form_id=form_id,
                submission_id=submission_id,
                org_id=org_id,
                created_by=created_by_id,
                form=form,  # type: ignore
                submission_data=answers,
            )

            if deal:
                # Update deal with processing results
                update_data = {
                    "exclusion_filter_result": exclusion_result,
                    "scoring": deal_data.get("scoring"),
                    "updated_at": int(datetime.now(timezone.utc).timestamp()),
                }

                # Add timeline events
                if exclusion_result.get("excluded"):
                    deal.add_timeline_event(
                        "Submission excluded",
                        exclusion_result.get("reason", "Matched exclusion filter"),
                    )
                elif thesis_scores:
                    deal.add_timeline_event(
                        f"Scored against {len(thesis_scores)} theses",
                        f"Best score: {deal_data['scoring']['normalized_score']:.1f}",
                    )
                else:
                    deal.add_timeline_event("No matching theses found")

                # Update the deal
                updated_deal = await deal_service.update_deal(
                    deal_id=deal.id, update_data=update_data
                )

                return updated_deal or deal

            return deal

        except Exception as e:
            logger.error(
                f"Error creating deal from processing: {str(e)}", exc_info=True
            )
            await self.handle_error(
                e,
                {
                    "submission_id": str(submission_id),
                    "form_id": str(form_id),
                    "org_id": str(org_id),
                },
            )
            return None

    async def enqueue_enrichment_jobs(
        self,
        deal_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
    ) -> Optional[str]:
        """Enqueue AI enrichment jobs for the deal."""
        queue_service: QueueService = await get_queue_service()  # type: ignore
        try:
            job_result = await queue_service.enqueue_job(
                job_type="deal_enrichment",
                payload={
                    "deal_id": str(deal_id),
                    "submission_id": str(submission_id),
                    "org_id": str(org_id),
                    "form_id": str(form_id),
                },
                queue_type=QueueType.AI,
                priority=JobPriority.NORMAL,
                metadata={
                    "job_description": f"AI enrichment for deal {deal_id}",
                    "entity_type": "deal",
                    "entity_id": str(deal_id),
                },
            )

            if job_result:
                logger.info(
                    f"Enqueued enrichment job {job_result.id} for deal {deal_id}"
                )
                return job_result.id
            else:
                logger.warning(f"Failed to enqueue enrichment job for deal {deal_id}")
                return None

        except Exception as e:
            logger.error(
                f"Error enqueuing enrichment jobs for deal {deal_id}: {str(e)}",
                exc_info=True,
            )
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "submission_id": str(submission_id),
                    "org_id": str(org_id),
                    "form_id": str(form_id),
                },
            )
            return None

    async def _create_initial_deal(
        self,
        submission_id: str,
        form_id: str,
        org_id: str,
        answers: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[str] = None,
    ) -> Optional[Deal]:
        """Create initial deal record before async processing.

        Uses atomic upsert to prevent race condition duplicate creation.
        """
        try:
            form_service: FormService = await get_form_service()  # type: ignore
            deal_service: DealService = await get_deal_service()  # type: ignore

            # Convert to ObjectIds for database operations
            submission_obj_id = ObjectId(submission_id)
            form_obj_id = ObjectId(form_id)
            org_obj_id = ObjectId(org_id)

            # ATOMIC OPERATION: Try to find existing deal with submission ID
            # Use MongoDB's findOneAndUpdate with upsert for atomic check-and-create
            existing_deal = await Deal.find_one(
                query={
                    "org_id": org_obj_id,
                    "submission_ids": {"$in": [submission_obj_id]},
                }
            )

            if existing_deal:
                logger.info(
                    f"Deal already exists for submission {submission_id}, using existing deal {existing_deal.id}"
                )
                # Convert back to Deal model
                return existing_deal

            # Get form details for core field extraction
            form = await form_service.get_form_with_details(form_id)
            if not form:
                logger.error(f"Form not found: {form_id}")
                return None

            # Set created_by or default to system user
            created_by_id = created_by or form.created_by

            # Extract core fields from submission
            core_fields = {}
            if form and answers:
                try:
                    core_fields = await deal_service._extract_core_fields(form, answers)
                except Exception as e:
                    logger.warning(f"Failed to extract core fields: {e}")
                    core_fields = {}

            # Prepare deal data for atomic creation
            deal_data = {
                "org_id": org_obj_id,
                "form_id": form_obj_id,
                "submission_ids": [submission_obj_id],
                "created_by": ObjectId(str(created_by_id)),
                "status": "new",
                "timeline": [
                    {
                        "date": datetime.now(timezone.utc).isoformat(),
                        "event": "Deal created from submission",
                        "notes": None,
                    }
                ],
                "created_at": int(datetime.now(timezone.utc).timestamp()),
                "updated_at": int(datetime.now(timezone.utc).timestamp()),
                **core_fields,
            }

            # ATOMIC UPSERT: Use findOneAndUpdate with upsert to prevent race condition
            # This will either find existing deal or create new one atomically
            # result = await Deal.update_one(
            #     {"org_id": org_obj_id, "submission_ids": {"$in": [submission_obj_id]}},
            #     {
            #         "$setOnInsert": deal_data,  # Only set on insert (new document)
            #         "$addToSet": {
            #             "submission_ids": submission_obj_id
            #         },  # Add submission ID if not present
            #     },  # Return the document after update/insert
            # )
            deal = Deal(**deal_data)
            deal = await deal.save()

            # Convert result to Deal model
            # deal = Deal(**result)

            # If this was a new creation (not an existing deal), log it
            if deal:
                logger.info(
                    f"Created new deal {deal.id} for submission {submission_id}"
                )

                # Migrate submission files to deal documents for new deals only
                try:
                    from app.services.factory import get_deal_document_service

                    doc_service = await get_deal_document_service()
                    migrated_docs = await doc_service.migrate_submission_files_to_deal(  # type: ignore
                        deal.id, org_obj_id, [submission_obj_id]
                    )
                    logger.info(
                        f"Migrated {len(migrated_docs)} documents to deal {deal.id}"
                    )
                except Exception as e:
                    logger.error(f"Failed to migrate documents for deal {deal.id}: {e}")
                    # Don't fail deal creation if document migration fails
            else:
                logger.info(
                    f"Using existing deal {deal.id} for submission {submission_id}"
                )

            return deal

        except Exception as e:
            logger.error(f"Error creating initial deal: {str(e)}", exc_info=True)
            await self.handle_error(
                e,
                {"submission_id": submission_id, "form_id": form_id, "org_id": org_id},
            )
            return None

    async def _process_submission_async(
        self,
        deal_id: str,
        submission_id: str,
        form_id: str,
        org_id: str,
        answers: Dict[str, Any],
    ) -> None:
        """Process submission with all steps running asynchronously and independently."""
        logger.info(f"Starting async processing for deal {deal_id}")

        # Create async tasks for each processing step
        tasks = [
            asyncio.create_task(
                self._process_exclusion_filters_async(
                    deal_id, form_id, org_id, answers
                ),
                name=f"exclusion_filters_{deal_id}",
            ),
            asyncio.create_task(
                self._process_thesis_matching_async(deal_id, form_id, org_id, answers),
                name=f"thesis_matching_{deal_id}",
            ),
            # PRD 2: Generate context block
            asyncio.create_task(
                self._queue_context_block_generation(deal_id),
                name=f"context_block_{deal_id}",
            ),
            asyncio.create_task(
                self._enqueue_enrichment_async(deal_id, submission_id, org_id, form_id),
                name=f"enrichment_{deal_id}",
            ),
        ]

        # Run all tasks concurrently and wait for completion
        # Each task handles its own errors and updates the deal independently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Log any exceptions that occurred
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(
                    f"Task {tasks[i].get_name()} failed: {result}", exc_info=result
                )

        logger.info(f"Completed {len(tasks)} async processing tasks for deal {deal_id}")

    async def _process_exclusion_filters_async(
        self,
        deal_id: str,
        form_id: str,
        org_id: str,
        answers: Dict[str, Any],
    ) -> None:
        deal_service: DealService = await get_deal_service()  # type: ignore
        exclusion_filter_service: ExclusionFilterService = (
            await get_exclusion_filter_service()
        )  # type: ignore
        """Process exclusion filters asynchronously."""
        try:
            logger.info(f"Processing exclusion filters for deal {deal_id}")

            # Add timeline event
            await deal_service.add_timeline_event(
                deal_id=deal_id, event="Exclusion filter check started"
            )

            # Fetch active exclusion filters
            exclusion_filters = await exclusion_filter_service.list_exclusion_filters(
                org_id=org_id, form_id=form_id, include_deleted=False
            )

            # Filter to only active filters
            active_filters = [
                f for f in exclusion_filters if f.is_active and not f.is_deleted
            ]

            logger.info(
                f"Found {len(active_filters)} active exclusion filters for deal {deal_id}"
            )

            # Evaluate each filter
            exclusion_result = {
                "excluded": False,
                "filter_id": None,
                "filter_name": None,
                "reason": None,
            }

            for exclusion_filter in active_filters:
                try:
                    if await self._evaluate_exclusion_filter(exclusion_filter, answers):
                        # Filter matched - submission should be excluded
                        exclusion_result = {
                            "excluded": True,
                            "filter_id": str(exclusion_filter.id),
                            "filter_name": exclusion_filter.name,
                            "reason": exclusion_filter.description
                            or f"Matched exclusion filter: {exclusion_filter.name}",
                        }
                        logger.info(
                            f"Deal {deal_id} excluded by filter {exclusion_filter.name}"
                        )
                        break
                except Exception as e:
                    logger.error(
                        f"Error evaluating exclusion filter {exclusion_filter.id}: {str(e)}",
                        exc_info=True,
                    )
                    # Continue with other filters
                    continue

            # Update deal with exclusion result
            update_data = {
                "exclusion_filter_result": exclusion_result,
                "updated_at": int(datetime.now(timezone.utc).timestamp()),
            }

            # Update status if excluded
            if exclusion_result["excluded"]:
                update_data["status"] = "excluded"

            await deal_service.update_deal(deal_id=deal_id, update_data=update_data)

            # Add timeline event
            if exclusion_result["excluded"]:
                await deal_service.add_timeline_event(
                    deal_id=deal_id,
                    event="Exclusion filter triggered",
                    notes=exclusion_result["reason"],
                )
            else:
                await deal_service.add_timeline_event(
                    deal_id=deal_id,
                    event="Exclusion filter check completed",
                    notes="No exclusion filters matched",
                )

            logger.info(f"Completed exclusion filter processing for deal {deal_id}")

        except Exception as e:
            logger.error(
                f"Error in exclusion filter processing for deal {deal_id}: {str(e)}",
                exc_info=True,
            )

            # Update deal with error
            await deal_service.update_deal(
                deal_id=deal_id,
                update_data={
                    "exclusion_filter_error": str(e),
                    "updated_at": int(datetime.now(timezone.utc).timestamp()),
                },
            )

            # Add timeline event
            await deal_service.add_timeline_event(
                deal_id=deal_id,
                event="Exclusion filter check failed",
                notes=f"Error: {str(e)}",
            )

    async def _process_thesis_matching_async(
        self,
        deal_id: str,
        form_id: str,
        org_id: str,
        answers: Dict[str, Any],
    ) -> None:
        """Process thesis matching and scoring asynchronously."""
        deal_service: DealService = await get_deal_service()  # type: ignore
        thesis_service: ThesisService = await get_thesis_service()  # type: ignore
        try:
            logger.info(f"Processing thesis matching for deal {deal_id}")

            # Add timeline event
            await deal_service.add_timeline_event(
                deal_id=deal_id, event="Thesis matching started"
            )

            # Check if deal was excluded first
            deal = await deal_service.get_deal(deal_id)
            if (
                deal
                and deal.exclusion_filter_result
                and deal.exclusion_filter_result.get("excluded")
            ):
                logger.info(f"Deal {deal_id} was excluded, skipping thesis matching")
                await deal_service.add_timeline_event(
                    deal_id=deal_id,
                    event="Thesis matching skipped",
                    notes="Deal was excluded by exclusion filters",
                )
                return

            # Fetch active theses for this form and org
            theses = await thesis_service.list_theses(
                org_id=org_id, form_id=form_id, is_active=True
            )

            # Filter to only active, non-deleted theses
            active_theses = [t for t in theses if t.is_active and not t.is_deleted]

            logger.info(f"Found {len(active_theses)} active theses for deal {deal_id}")

            # Find matching theses
            matching_theses = []
            for thesis in active_theses:
                try:
                    if await self._evaluate_thesis_match_rules(thesis, answers):
                        matching_theses.append(thesis)
                        logger.info(f"Thesis {thesis.name} matches deal {deal_id}")
                except Exception as e:
                    logger.error(
                        f"Error evaluating thesis {thesis.id}: {str(e)}", exc_info=True
                    )
                    # Continue with other theses
                    continue

            logger.info(
                f"Found {len(matching_theses)} matching theses for deal {deal_id}"
            )

            # Score against matching theses
            thesis_scores = []
            if matching_theses:
                # Score each thesis concurrently
                scoring_tasks = [
                    asyncio.create_task(
                        self._score_thesis_async(thesis, answers),
                        name=f"score_thesis_{thesis.id}",
                    )
                    for thesis in matching_theses
                ]

                # Wait for all scoring to complete
                scoring_results = await asyncio.gather(
                    *scoring_tasks, return_exceptions=True
                )

                # Process results
                for i, result in enumerate(scoring_results):
                    if isinstance(result, Exception):
                        logger.error(
                            f"Error scoring thesis {matching_theses[i].id}: {str(result)}"
                        )
                        continue
                    elif result:
                        thesis_scores.append(result)

            # Prepare comprehensive scoring data
            scoring_data = {
                "thesis": {
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "thesis_matches": [],
                    "best_thesis_id": None,
                    "matching_count": 0,
                },
                "founders": {
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "ai_analysis": "Founder analysis pending",
                },
                "market": {
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "ai_analysis": "Market analysis pending",
                },
            }

            if thesis_scores:
                # Find the highest scoring thesis
                best_score = max(
                    thesis_scores, key=lambda x: x.get("normalized_score", 0)
                )

                # Extract comprehensive scoring if available
                comprehensive_scoring = best_score.get("comprehensive_scoring")

                if comprehensive_scoring:
                    # Use comprehensive scoring structure
                    scoring_data = comprehensive_scoring
                else:
                    # Fallback to legacy structure
                    scoring_data["thesis"] = {
                        "total_score": best_score.get("total_score", 0),
                        "normalized_score": best_score.get("normalized_score", 0),
                        "thesis_matches": thesis_scores,
                        "best_thesis_id": best_score.get("thesis_id"),
                        "matching_count": len(thesis_scores),
                    }

            # Update deal with scoring results
            update_data = {
                "scoring": scoring_data,
                "updated_at": int(datetime.now(timezone.utc).timestamp()),
            }

            await deal_service.update_deal(deal_id=deal_id, update_data=update_data)

            # Add timeline event
            if thesis_scores:
                best_thesis_score = scoring_data.get("thesis", {}).get(
                    "normalized_score", 0
                )
                await deal_service.add_timeline_event(
                    deal_id=deal_id,
                    event="Thesis matching completed",
                    notes=f"Matched {len(thesis_scores)} theses, best score: {best_thesis_score:.1f}",
                )
            else:
                await deal_service.add_timeline_event(
                    deal_id=deal_id,
                    event="Thesis matching completed",
                    notes="No matching theses found",
                )

            logger.info(f"Completed thesis matching for deal {deal_id}")

        except Exception as e:
            logger.error(
                f"Error in thesis matching for deal {deal_id}: {str(e)}", exc_info=True
            )

            # Update deal with error
            await deal_service.update_deal(
                deal_id=deal_id,
                update_data={
                    "thesis_matching_error": str(e),
                    "updated_at": int(datetime.now(timezone.utc).timestamp()),
                },
            )

            # Add timeline event
            await deal_service.add_timeline_event(
                deal_id=deal_id,
                event="Thesis matching failed",
                notes=f"Error: {str(e)}",
            )

    async def _enqueue_enrichment_async(
        self,
        deal_id: str,
        submission_id: str,
        org_id: str,
        form_id: str,
    ) -> None:
        """Enqueue enrichment jobs asynchronously."""
        deal_service: DealService = await get_deal_service()  # type: ignore
        # queue_service: QueueService = await get_queue_service()  # type: ignore
        try:
            logger.info(f"Enqueuing enrichment for deal {deal_id}")

            # Add timeline event
            await deal_service.add_timeline_event(
                deal_id=deal_id, event="AI enrichment queued"
            )

            # Enqueue enrichment job
            # job_result = await queue_service.enqueue_job(
            #     job_type="deal_enrichment",
            #     payload={
            #         "deal_id": deal_id,
            #         "submission_id": submission_id,
            #         "org_id": org_id,
            #         "form_id": form_id,
            #     },
            #     queue_type=QueueType.AI,
            #     priority=JobPriority.NORMAL,
            #     metadata={
            #         "job_description": f"AI enrichment for deal {deal_id}",
            #         "entity_type": "deal",
            #         "entity_id": deal_id,
            #     },
            # )

            # if job_result:
            #     logger.info(
            #         f"Enqueued enrichment job {job_result.id} for deal {deal_id}"
            #     )

            # Update deal with enrichment job info
            await deal_service.update_deal(
                deal_id=deal_id,
                update_data={
                    "enrichment_status": "coming_soon",
                    "updated_at": int(datetime.now(timezone.utc).timestamp()),
                },
            )
            # else:
            #     logger.warning(f"Failed to enqueue enrichment job for deal {deal_id}")

        except Exception as e:
            logger.error(
                f"Error enqueuing enrichment for deal {deal_id}: {str(e)}",
                exc_info=True,
            )

            # Update deal with error
            await deal_service.update_deal(
                deal_id=deal_id,
                update_data={
                    "enrichment_error": str(e),
                    "updated_at": int(datetime.now(timezone.utc).timestamp()),
                },
            )

            # Add timeline event
            await deal_service.add_timeline_event(
                deal_id=deal_id,
                event="AI enrichment failed to queue",
                notes=f"Error: {str(e)}",
            )

    async def _evaluate_exclusion_filter(
        self, exclusion_filter, answers: Dict[str, Any]
    ) -> bool:
        """Evaluate a single exclusion filter against submission answers."""
        try:
            # Get the root operator (and/or)
            operator = exclusion_filter.operator.lower()
            conditions = exclusion_filter.conditions

            if not conditions:
                return False

            # Evaluate each condition
            condition_results = []
            for condition in conditions:
                try:
                    result = await self._evaluate_condition(condition, answers)
                    condition_results.append(result)
                except Exception as e:
                    logger.error(f"Error evaluating condition {condition}: {str(e)}")
                    condition_results.append(False)  # Default to false on error

            # Apply the operator
            if operator == "and":
                return all(condition_results)
            elif operator == "or":
                return any(condition_results)
            else:
                logger.warning(f"Unknown operator {operator}, defaulting to 'and'")
                return all(condition_results)

        except Exception as e:
            logger.error(
                f"Error evaluating exclusion filter {exclusion_filter.id}: {str(e)}"
            )
            return False

    async def _evaluate_thesis_match_rules(
        self,
        thesis,
        answers: Dict[str, Any],
    ) -> bool:
        """Evaluate thesis match rules against submission answers."""
        thesis_service: ThesisService = await get_thesis_service()  # type: ignore
        try:
            # Get thesis with rules
            thesis_with_rules = await thesis_service.get_thesis_with_rules(thesis.id)
            if not thesis_with_rules or not thesis_with_rules.match_rules:
                # No match rules means it matches everything
                return True

            # Evaluate each match rule
            for match_rule in thesis_with_rules.match_rules:
                try:
                    # Each match rule has its own operator and conditions
                    # operator = getattr(match_rule, "operator", "and").lower()
                    condition = getattr(match_rule, "condition", None)

                    if condition:
                        result = await self._evaluate_condition(condition, answers)
                        if not result:
                            return False  # If any rule fails, thesis doesn't match
                except Exception as e:
                    logger.error(
                        f"Error evaluating match rule {match_rule.id}: {str(e)}"
                    )
                    return False  # Default to false on error

            return True  # All rules passed

        except Exception as e:
            logger.error(
                f"Error evaluating thesis match rules for {thesis.id}: {str(e)}"
            )
            return False

    async def _score_thesis_async(
        self,
        thesis,
        answers: Dict[str, Any],
    ) -> Optional[Dict[str, Any]]:
        """Score a single thesis against submission answers."""
        thesis_service: ThesisService = await get_thesis_service()  # type: ignore
        try:
            score_result = await thesis_service.calculate_score(
                thesis_id=thesis.id, form_responses=answers
            )

            if score_result and "error" not in score_result:
                # Extract comprehensive scoring if available
                comprehensive_scoring = score_result.get("comprehensive_scoring")

                result = {
                    "thesis_id": str(thesis.id),
                    "thesis_name": thesis.name,
                    **score_result,
                }

                # Add comprehensive scoring data if available
                if comprehensive_scoring:
                    result["comprehensive_scoring"] = comprehensive_scoring

                return result
            else:
                logger.warning(
                    f"Failed to score thesis {thesis.id}: {score_result.get('error', 'Unknown error')}"
                )
                return None

        except Exception as e:
            logger.error(f"Error scoring thesis {thesis.id}: {str(e)}", exc_info=True)
            return None

    async def _evaluate_condition(
        self, condition: Dict[str, Any], answers: Dict[str, Any]
    ) -> bool:
        """Evaluate a single condition against submission answers."""
        try:
            question_id = condition.get("question_id")
            operator = condition.get("operator")
            expected_value = condition.get("value")

            if not all([question_id, operator]):
                logger.warning(f"Invalid condition: {condition}")
                return False

            # Get the actual answer
            actual_value = answers.get(str(question_id))

            # Handle different operators
            if operator == "eq":
                return actual_value == expected_value
            elif operator == "ne":
                return actual_value != expected_value
            elif operator == "gt":
                return self._safe_numeric_compare(
                    actual_value, expected_value, lambda a, e: a > e
                )
            elif operator == "lt":
                return self._safe_numeric_compare(
                    actual_value, expected_value, lambda a, e: a < e
                )
            elif operator == "gte":
                return self._safe_numeric_compare(
                    actual_value, expected_value, lambda a, e: a >= e
                )
            elif operator == "lte":
                return self._safe_numeric_compare(
                    actual_value, expected_value, lambda a, e: a <= e
                )
            elif operator == "in":
                if isinstance(expected_value, list):
                    return actual_value in expected_value
                else:
                    return str(actual_value) == str(expected_value)
            elif operator == "not_in":
                if isinstance(expected_value, list):
                    return actual_value not in expected_value
                else:
                    return str(actual_value) != str(expected_value)
            elif operator == "contains":
                return (
                    str(expected_value).lower() in str(actual_value).lower()
                    if actual_value
                    else False
                )
            elif operator == "not_contains":
                return (
                    str(expected_value).lower() not in str(actual_value).lower()
                    if actual_value
                    else True
                )
            else:
                logger.warning(f"Unknown operator {operator}")
                return False

        except Exception as e:
            logger.error(f"Error evaluating condition {condition}: {str(e)}")
            return False

    def _safe_numeric_compare(self, actual, expected, compare_func) -> bool:
        """Safely compare numeric values."""
        try:
            if actual is None or expected is None:
                return False

            # Convert to float for comparison
            actual_num = float(actual)
            expected_num = float(expected)

            return compare_func(actual_num, expected_num)
        except (ValueError, TypeError):
            # If conversion fails, fall back to string comparison
            return compare_func(str(actual), str(expected))

    async def _queue_context_block_generation(self, deal_id: str) -> None:
        """Queue context block generation job (PRD 2)."""
        try:
            logger.info(f"Queueing context block generation for deal {deal_id}")

            queue_service: RedisQueueService = await get_queue_service()  # type: ignore

            # Queue context block generation job
            await queue_service.enqueue_job(
                job_type="generate_context_block",
                payload={
                    "deal_id": deal_id,
                },
                metadata={
                    "job_description": f"Generate context block for deal {deal_id}",
                    "entity_type": "deal",
                    "entity_id": deal_id,
                },
            )

            logger.info(f"Queued context block generation for deal {deal_id}")

        except Exception as e:
            logger.error(
                f"Error queueing context block generation for deal {deal_id}: {str(e)}"
            )
            # Don't fail the entire submission processing if context block generation fails
