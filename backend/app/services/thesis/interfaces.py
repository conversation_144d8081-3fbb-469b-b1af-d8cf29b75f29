"""
Investment Thesis Service Interface

This module defines the interface for investment thesis services, including
methods for managing theses, scoring rules, and match rules.
"""

from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union

from bson import ObjectId

from app.models.thesis import (
    InvestmentThesis,
    MatchRule,
    ScoringRule,
    ThesisStatus,
    ThesisWithRules,
)

# Type variables for generic functions
T = TypeVar("T", ScoringRule, MatchRule)


class ThesisServiceInterface(ABC):
    """Interface for investment thesis services."""

    @abstractmethod
    async def get_thesis(
        self, thesis_id: Union[str, ObjectId]
    ) -> Optional[InvestmentThesis]:
        """
        Get thesis by ID.

        Args:
            thesis_id: ID of the thesis to retrieve

        Returns:
            The thesis if found, None otherwise
        """
        pass

    @abstractmethod
    async def get_thesis_with_rules(
        self, thesis_id: Union[str, ObjectId]
    ) -> Optional[ThesisWithRules]:
        """
        Get thesis by ID with all rules expanded.

        Args:
            thesis_id: ID of the thesis to retrieve

        Returns:
            The thesis with rules if found, None otherwise
        """
        pass

    @abstractmethod
    async def create_thesis(
        self,
        name: str,
        description: str,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        status: ThesisStatus = ThesisStatus.DRAFT,
        is_active: bool = True,
    ) -> InvestmentThesis:
        """
        Create a new investment thesis.

        Args:
            name: Name of the thesis
            description: Description of the thesis
            form_id: ID of the form this thesis applies to
            org_id: ID of the organization that owns this thesis
            created_by: ID of the user who created this thesis
            status: Status of the thesis
            is_active: Whether this thesis is active

        Returns:
            The created thesis
        """
        pass

    @abstractmethod
    async def update_thesis(
        self, thesis_id: Union[str, ObjectId], update_data: Dict[str, Any]
    ) -> Optional[InvestmentThesis]:
        """
        Update an existing thesis.

        Args:
            thesis_id: ID of the thesis to update
            update_data: Dictionary of fields to update

        Returns:
            The updated thesis if found, None otherwise
        """
        pass

    @abstractmethod
    async def delete_thesis(self, thesis_id: Union[str, ObjectId]) -> bool:
        """
        Delete a thesis and all its rules.

        Args:
            thesis_id: ID of the thesis to delete

        Returns:
            True if the thesis was deleted, False otherwise
        """
        pass

    @abstractmethod
    async def list_theses(
        self,
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
        form_id: Optional[Union[str, ObjectId]] = None,
        status: Optional[ThesisStatus] = None,
        is_active: Optional[bool] = None,
    ) -> List[InvestmentThesis]:
        """
        List theses with optional filtering.

        Args:
            org_id: ID of the organization to list theses for
            skip: Number of theses to skip
            limit: Maximum number of theses to return
            form_id: Optional filter by form ID
            status: Optional filter by status
            is_active: Optional filter by active status

        Returns:
            List of theses matching the filters
        """
        pass

    @abstractmethod
    async def create_scoring_rule(
        self, thesis_id: Union[str, ObjectId], **kwargs
    ) -> ScoringRule:
        """
        Create a new scoring rule for a thesis.

        Args:
            thesis_id: ID of the thesis this rule belongs to
            question_id: ID of the question this rule applies to
            weight: Weight of this rule in the overall score
            expected_value: Expected value for scoring
            condition: Condition for scoring
            is_repeatable: Whether this question is in a repeatable section
            aggregation_type: How to aggregate scores for repeatable sections
            aggregation_field: Field to aggregate on for repeatable sections
            filters: Filters for repeatable section items
            exclude_from_scoring: Whether to exclude this question from scoring
            rule_type: Type of rule (scoring or bonus)
            bonus_points: Points to award for bonus rules

        Returns:
            The created scoring rule
        """
        pass

    @abstractmethod
    async def update_scoring_rule(
        self, rule_id: Union[str, ObjectId], update_data: Dict[str, Any]
    ) -> Optional[ScoringRule]:
        """
        Update an existing scoring rule.

        Args:
            rule_id: ID of the rule to update
            update_data: Dictionary of fields to update

        Returns:
            The updated rule if found, None otherwise
        """
        pass

    @abstractmethod
    async def delete_scoring_rule(self, rule_id: Union[str, ObjectId]) -> bool:
        """
        Delete a scoring rule.

        Args:
            rule_id: ID of the rule to delete

        Returns:
            True if the rule was deleted, False otherwise
        """
        pass

    @abstractmethod
    async def get_scoring_rule(
        self, rule_id: Union[str, ObjectId]
    ) -> Optional[ScoringRule]:
        """
        Get a scoring rule by ID.

        Args:
            rule_id: ID of the rule to retrieve

        Returns:
            The rule if found, None otherwise
        """
        pass

    @abstractmethod
    async def list_scoring_rules(
        self,
        thesis_id: Union[str, ObjectId],
        exclude_from_scoring: Optional[bool] = None,
        rule_type: Optional[str] = None,
    ) -> List[ScoringRule]:
        """
        List scoring rules for a thesis with optional filtering.

        Args:
            thesis_id: ID of the thesis to list rules for
            exclude_from_scoring: Optional filter by exclude_from_scoring
            rule_type: Optional filter by rule_type

        Returns:
            List of scoring rules matching the filters
        """
        pass

    @abstractmethod
    async def create_match_rule(
        self, thesis_id: Union[str, ObjectId], **kwargs
    ) -> MatchRule:
        """
        Create a new match rule for a thesis.

        Args:
            thesis_id: ID of the thesis this rule belongs to
            name: Name of the rule
            description: Description of the rule
            operator: Logical operator for conditions
            conditions: List of conditions

        Returns:
            The created match rule
        """
        pass

    @abstractmethod
    async def update_match_rule(
        self, rule_id: Union[str, ObjectId], update_data: Dict[str, Any]
    ) -> Optional[MatchRule]:
        """
        Update an existing match rule.

        Args:
            rule_id: ID of the rule to update
            update_data: Dictionary of fields to update

        Returns:
            The updated rule if found, None otherwise
        """
        pass

    @abstractmethod
    async def delete_match_rule(self, rule_id: Union[str, ObjectId]) -> bool:
        """
        Delete a match rule.

        Args:
            rule_id: ID of the rule to delete

        Returns:
            True if the rule was deleted, False otherwise
        """
        pass

    @abstractmethod
    async def get_match_rule(
        self, rule_id: Union[str, ObjectId]
    ) -> Optional[MatchRule]:
        """
        Get a match rule by ID.

        Args:
            rule_id: ID of the rule to retrieve

        Returns:
            The rule if found, None otherwise
        """
        pass

    @abstractmethod
    async def list_match_rules(
        self, thesis_id: Union[str, ObjectId]
    ) -> List[MatchRule]:
        """
        List match rules for a thesis.

        Args:
            thesis_id: ID of the thesis to list rules for

        Returns:
            List of match rules for the thesis
        """
        pass

    @abstractmethod
    async def calculate_score(
        self, thesis_id: Union[str, ObjectId], form_responses: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Calculate score for a form response based on thesis rules.

        Args:
            thesis_id: ID of the thesis to calculate score for
            form_responses: Dictionary of form responses

        Returns:
            Dictionary with score details
        """
        pass

    @abstractmethod
    async def find_matching_theses(
        self,
        form_id: Union[str, ObjectId],
        form_responses: Dict[str, Any],
        org_id: Union[str, ObjectId],
    ) -> List[InvestmentThesis]:
        """
        Find all theses that match a form response based on their match rules.

        Args:
            form_id: ID of the form the responses are for
            form_responses: Dictionary of form responses
            org_id: ID of the organization to find theses for

        Returns:
            List of matching theses
        """
        pass

    @abstractmethod
    async def preview_form_questions(
        self,
        form_id: Union[str, ObjectId],
        thesis_id: Optional[Union[str, ObjectId]] = None,
    ) -> Dict[str, Any]:
        """
        Preview form questions with their scoring configuration.

        Args:
            form_id: ID of the form to preview
            thesis_id: Optional ID of a thesis to include configuration from

        Returns:
            Dictionary with form questions and their configuration
        """
        pass

    @abstractmethod
    async def validate_thesis_ownership(
        self, thesis_id: Union[str, ObjectId], org_id: Union[str, ObjectId]
    ) -> InvestmentThesis:
        """
        Validate that a thesis exists and belongs to the organization.

        Args:
            thesis_id: ID of the thesis to validate
            org_id: Organization ID to check ownership against

        Returns:
            The thesis if it exists and belongs to the organization

        Raises:
            ValueError: If thesis doesn't exist or doesn't belong to the organization
        """
        pass

    @abstractmethod
    async def validate_rule_ownership(
        self,
        rule_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        get_rule_func: Callable,
        rule_type: str = "rule",
    ) -> Union[ScoringRule, MatchRule]:
        """
        Validate that a rule exists and belongs to an organization's thesis.

        Args:
            rule_id: ID of the rule to validate
            org_id: Organization ID to check ownership against
            get_rule_func: Function to get the rule (get_scoring_rule or get_match_rule)
            rule_type: Type of rule for error messages

        Returns:
            The rule if it exists and belongs to the organization's thesis

        Raises:
            ValueError: If rule doesn't exist or doesn't belong to the organization's thesis
        """
        pass

    @abstractmethod
    async def create_thesis_scoring_rules(
        self, thesis_id: Union[str, ObjectId], rules_data: List[Dict[str, Any]]
    ) -> List[ScoringRule]:
        """
        Create scoring rules for a thesis.

        Args:
            thesis_id: ID of the thesis to create rules for
            rules_data: List of rule data dictionaries

        Returns:
            List of created scoring rules
        """
        pass

    @abstractmethod
    async def create_thesis_match_rules(
        self, thesis_id: Union[str, ObjectId], rules_data: List[Dict[str, Any]]
    ) -> List[MatchRule]:
        """
        Create match rules for a thesis.

        Args:
            thesis_id: ID of the thesis to create rules for
            rules_data: List of rule data dictionaries

        Returns:
            List of created match rules
        """
        pass

    @abstractmethod
    async def update_thesis_scoring_rules(
        self, thesis_id: Union[str, ObjectId], scoring_rules: List[Dict[str, Any]]
    ) -> None:
        """
        Update scoring rules for a thesis.

        Args:
            thesis_id: ID of the thesis to update rules for
            scoring_rules: List of rule data dictionaries
        """
        pass

    @abstractmethod
    async def update_thesis_match_rules(
        self, thesis_id: Union[str, ObjectId], match_rules: List[Dict[str, Any]]
    ) -> None:
        """
        Update match rules for a thesis.

        Args:
            thesis_id: ID of the thesis to update rules for
            match_rules: List of rule data dictionaries
        """
        pass

    @abstractmethod
    async def create_demo_thesis(
        self, org_id: Union[str, ObjectId], form_id: Union[str, ObjectId]
    ) -> Optional[InvestmentThesis]:
        """
        Create a comprehensive demo thesis for the default form.

        This method creates a demo thesis that demonstrates:
        - Match rules with simple conditions
        - Scoring rules for different question types
        - Bonus rules for special cases
        - Exclusion filters for filtering out submissions

        Args:
            org_id: ID of the organization
            form_id: ID of the form to create thesis for

        Returns:
            The created thesis if successful, None otherwise
        """
        pass
