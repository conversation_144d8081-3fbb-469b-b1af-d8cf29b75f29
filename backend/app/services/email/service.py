from pathlib import Path
from typing import Optional

import httpx
from app.core.config import settings
from app.core.logging import get_logger
from app.services.email.interface import IEmailService
from fastapi import HTTPException, status
from fastapi_mail import ConnectionConfig, FastMail, MessageSchema  # type: ignore

logger = get_logger(__name__)


class EmailService(IEmailService):
    def __init__(self):
        self.use_resend = settings.USE_RESEND and settings.RESEND_API_KEY
        self.templates_dir = Path(__file__).parent.parent.parent / "templates"

        if not self.use_resend:
            # Initialize FastMail for SMTP
            self.config = ConnectionConfig(
                MAIL_USERNAME=settings.SMTP_USERNAME,
                MAIL_PASSWORD=settings.SMTP_PASSWORD,
                MAIL_FROM=settings.SMTP_FROM,
                MAIL_PORT=settings.SMTP_PORT,
                MAIL_SERVER=settings.SMTP_HOST,
                MAIL_FROM_NAME=settings.SMTP_FROM_NAME,
                MAIL_STARTTLS=True,
                MAIL_SSL_TLS=False,
                USE_CREDENTIALS=True,
                VALIDATE_CERTS=True,
            )
            self.fastmail = FastMail(self.config)
        else:
            self.fastmail = None
            logger.info("Using Resend for email delivery")

    def _load_template(self, template_name: str, **kwargs) -> str:
        """Load and render HTML email template."""
        try:
            template_path = self.templates_dir / f"{template_name}.html"
            if not template_path.exists():
                logger.warning(
                    f"Template {template_name}.html not found, using fallback"
                )
                return self._get_fallback_template(template_name, **kwargs)

            with open(template_path, "r", encoding="utf-8") as f:
                template_content = f.read()

            # Simple template variable replacement
            for key, value in kwargs.items():
                template_content = template_content.replace(
                    f"{{{{{key}}}}}", str(value)
                )

            return template_content
        except Exception as e:
            logger.error(f"Error loading template {template_name}: {str(e)}")
            return self._get_fallback_template(template_name, **kwargs)

    def _get_fallback_template(self, template_name: str, **kwargs) -> str:
        """Get fallback template if main template fails to load."""
        if template_name == "forgot_password":
            return f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Reset Your Password - TractionX</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                </style>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #ffffff;">
                <div style="width: 100%; max-width: 650px; margin: 0 auto; background: #ffffff;">
                    <div style="background: #000000; padding: 40px; text-align: center;">
                        <img src="https://www.tractionx.ai/logo_wo_wordmark_black.png" alt="TractionX Logo" style="height: 50px; width: auto; filter: invert(1); margin-bottom: 20px;">
                        <h1 style="color: #ffffff; font-size: 32px; font-weight: 700; margin: 0;">TractionX</h1>
                    </div>
                    <div style="padding: 50px 40px; background: #ffffff;">
                        <h2 style="color: #000000; font-size: 28px; font-weight: 700; margin: 0 0 24px 0; text-align: center;">Reset Your Password</h2>
                        <p style="color: #666666; font-size: 18px; margin: 0 0 32px 0; text-align: center; line-height: 1.6;">Click the button below to reset your password:</p>
                        <div style="text-align: center; margin: 50px 0;">
                            <a href="{kwargs.get("reset_url", "#")}" style="display: inline-block; background: #000000; color: #ffffff; padding: 18px 36px; text-decoration: none; border-radius: 12px; font-weight: 700; font-size: 18px; border: 2px solid #000000;">Reset Password</a>
                        </div>
                        <p style="color: #666666; font-size: 14px; text-align: center;">If you didn't request this, please ignore this email. This link will expire in 2 hours.</p>
                    </div>
                    <div style="background: #000000; padding: 30px 40px; text-align: center;">
                        <a href="https://www.tractionx.ai" style="color: #ffffff; text-decoration: none; font-size: 14px; font-weight: 600;">Visit TractionX.ai</a>
                    </div>
                </div>
            </body>
            </html>
            """
        elif template_name == "invite":
            return f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>You're Invited - TractionX</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                </style>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #ffffff;">
                <div style="width: 100%; max-width: 650px; margin: 0 auto; background: #ffffff;">
                    <div style="background: #000000; padding: 40px; text-align: center;">
                        <img src="https://www.tractionx.ai/logo_wo_wordmark_black.png" alt="TractionX Logo" style="height: 50px; width: auto; filter: invert(1); margin-bottom: 20px;">
                        <h1 style="color: #ffffff; font-size: 32px; font-weight: 700; margin: 0;">TractionX</h1>
                    </div>
                    <div style="padding: 50px 40px; background: #ffffff;">
                        <h2 style="color: #000000; font-size: 28px; font-weight: 700; margin: 0 0 24px 0; text-align: center;">You're Invited to TractionX!</h2>
                        <p style="color: #666666; font-size: 18px; margin: 0 0 32px 0; text-align: center; line-height: 1.6;">You've been invited to join TractionX. Click the button below to accept:</p>
                        <div style="text-align: center; margin: 50px 0;">
                            <a href="{kwargs.get("accept_url", "#")}" style="display: inline-block; background: #000000; color: #ffffff; padding: 18px 36px; text-decoration: none; border-radius: 12px; font-weight: 700; font-size: 18px; border: 2px solid #000000;">Accept Invitation</a>
                        </div>
                        <p style="color: #666666; font-size: 14px; text-align: center;">This invitation will expire in 7 days.</p>
                    </div>
                    <div style="background: #000000; padding: 30px 40px; text-align: center;">
                        <a href="https://www.tractionx.ai" style="color: #ffffff; text-decoration: none; font-size: 14px; font-weight: 600;">Visit TractionX.ai</a>
                    </div>
                </div>
            </body>
            </html>
            """
        else:
            return """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>TractionX</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                </style>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #ffffff;">
                <div style="width: 100%; max-width: 650px; margin: 0 auto; background: #ffffff;">
                    <div style="background: #000000; padding: 40px; text-align: center;">
                        <img src="https://www.tractionx.ai/logo_wo_wordmark_black.png" alt="TractionX Logo" style="height: 50px; width: auto; filter: invert(1); margin-bottom: 20px;">
                        <h1 style="color: #ffffff; font-size: 32px; font-weight: 700; margin: 0;">TractionX</h1>
                    </div>
                    <div style="padding: 50px 40px; background: #ffffff;">
                        <p style="color: #666666; font-size: 18px; text-align: center;">This is a message from TractionX.</p>
                    </div>
                    <div style="background: #000000; padding: 30px 40px; text-align: center;">
                        <a href="https://www.tractionx.ai" style="color: #ffffff; text-decoration: none; font-size: 14px; font-weight: 600;">Visit TractionX.ai</a>
                    </div>
                </div>
            </body>
            </html>
            """

    async def _send_via_resend(self, to: str, subject: str, html_content: str) -> None:
        """Send email via Resend API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.resend.com/emails",
                    headers={
                        "Authorization": f"Bearer {settings.RESEND_API_KEY}",
                        "Content-Type": "application/json",
                    },
                    json={
                        "from": f"TractionX <outreach@{settings.RESEND_DOMAIN}>",
                        "to": [to],
                        "subject": subject,
                        "html": html_content,
                    },
                )

                if response.status_code != 200:
                    logger.error(
                        f"Resend API error: {response.status_code} - {response.text}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Failed to send email via Resend",
                    )

                logger.info(f"Email sent via Resend to {to}")

        except httpx.RequestError as e:
            logger.error(f"Resend API request error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send email via Resend",
            )

    async def _send_email(self, to: str, subject: str, html_content: str) -> None:
        """Send email using configured service (Resend or SMTP)."""
        if self.use_resend:
            await self._send_via_resend(to, subject, html_content)
        else:
            # Use FastMail/SMTP
            if self.fastmail is None:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Email service not configured",
                )
            message = MessageSchema(
                subject=subject, recipients=[to], body=html_content, subtype="html"
            )
            await self.fastmail.send_message(message)
            logger.info(f"Email sent via SMTP to {to}")

    async def send_magic_link_email(
        self,
        email: str,
        magic_link_url: str,
        investor_name: Optional[str] = None,
        form_name: Optional[str] = None,
    ) -> None:
        """Send magic link email for shared form access."""
        try:
            subject = (
                f"Access Your Form{f' from {investor_name}' if investor_name else ''}"
            )

            html_content = f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h1 style="color: #2563eb;">Access Your Form</h1>
                        {f"<p>You have been invited by <strong>{investor_name}</strong> to fill out a form.</p>" if investor_name else ""}
                        {f"<p><strong>Form:</strong> {form_name}</p>" if form_name else ""}
                        <p>Click the link below to access the form:</p>
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{magic_link_url}"
                               style="background-color: #2563eb; color: white; padding: 12px 24px;
                                      text-decoration: none; border-radius: 6px; display: inline-block;">
                                Access Form
                            </a>
                        </div>
                        <p style="color: #666; font-size: 14px;">
                            This link will expire in 15 minutes for security reasons.
                        </p>
                        <p style="color: #666; font-size: 14px;">
                            If you didn't request this, please ignore this email.
                        </p>
                        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                        <p style="color: #999; font-size: 12px; text-align: center;">
                            Powered by TractionX
                        </p>
                    </div>
                </body>
            </html>
            """

            await self._send_email(email, subject, html_content)
            logger.info(f"Magic link email sent to {email}")

        except Exception as e:
            logger.error(f"Failed to send magic link email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send magic link email",
            )

    async def send_password_reset_email(self, email: str, token: str) -> None:
        """Send password reset email using beautiful HTML template."""
        try:
            reset_url = f"{settings.FRONTEND_URL}/reset-password?token={token}"

            # Load the beautiful template
            html_content = self._load_template(
                "forgot_password", email=email, reset_url=reset_url
            )

            subject = "Oops! Forgot Something? 🤦‍♂️ - TractionX Password Reset"

            await self._send_email(email, subject, html_content)
            logger.info(f"Password reset email sent to {email}")

        except Exception as e:
            logger.error(f"Failed to send password reset email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send password reset email",
            )

    async def send_invitation_email(
        self,
        email: str,
        token: str,
        invited_by_name: str = "TractionX Team",
        organization_name: str = "TractionX",
        role_name: str = "Team Member",
    ) -> None:
        """Send user invitation email using beautiful HTML template."""
        try:
            accept_url = f"{settings.FRONTEND_URL}/accept-invite?token={token}"

            # Load the beautiful template
            html_content = self._load_template(
                "invite",
                email=email,
                accept_url=accept_url,
                invited_by_name=invited_by_name,
                organization_name=organization_name,
                role_name=role_name,
            )

            subject = f"🎉 You're Invited to Join {organization_name} on TractionX!"

            await self._send_email(email, subject, html_content)
            logger.info(f"Invitation email sent to {email}")

        except Exception as e:
            logger.error(f"Failed to send invitation email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send invitation email",
            )

    async def send_welcome_email(self, email: str, name: Optional[str] = None) -> None:
        """Send welcome email to new user."""
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Welcome to TractionX</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                </style>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #ffffff;">
                
                <!-- Email Container -->
                <div style="width: 100%; max-width: 650px; margin: 0 auto; background: #ffffff;">
                    
                    <!-- Header Section with Logo -->
                    <div style="background: #000000; padding: 40px; text-align: center;">
                        <img src="https://www.tractionx.ai/logo_wo_wordmark_black.png" alt="TractionX Logo" style="height: 50px; width: auto; filter: invert(1); margin-bottom: 20px;">
                        <h1 style="color: #ffffff; font-size: 32px; font-weight: 700; margin: 0; letter-spacing: -0.02em;">
                            TractionX
                        </h1>
                        <p style="color: #ffffff; font-size: 16px; margin: 8px 0 0 0; opacity: 0.9;">
                            The First Agentic Operating System for Private Markets
                        </p>
                    </div>

                    <!-- Main Content -->
                    <div style="padding: 50px 40px; background: #ffffff;">
                        
                        <h2 style="color: #000000; font-size: 28px; font-weight: 700; margin: 0 0 24px 0; text-align: center; letter-spacing: -0.01em;">
                            Welcome to TractionX{f", {name}" if name else ""}! 🎉
                        </h2>

                        <p style="color: #666666; font-size: 18px; margin: 0 0 32px 0; text-align: center; line-height: 1.6;">
                            We're excited to have you on board. Get ready to transform how you discover, analyze, and manage investment opportunities.
                        </p>

                        <!-- Getting Started -->
                        <div style="background: #f8f8f8; border-radius: 12px; padding: 32px; margin: 32px 0;">
                            <h3 style="color: #000000; font-size: 20px; font-weight: 600; margin: 0 0 20px 0; text-align: center;">
                                🚀 Getting Started
                            </h3>
                            <div style="text-align: left; max-width: 400px; margin: 0 auto;">
                                <div style="margin-bottom: 16px; display: flex; align-items: flex-start;">
                                    <span style="color: #000000; font-weight: 700; margin-right: 12px; font-size: 18px;">1.</span>
                                    <span style="color: #333333; font-size: 16px;">Set up your organization profile</span>
                                </div>
                                <div style="margin-bottom: 16px; display: flex; align-items: flex-start;">
                                    <span style="color: #000000; font-weight: 700; margin-right: 12px; font-size: 18px;">2.</span>
                                    <span style="color: #333333; font-size: 16px;">Configure your investment thesis</span>
                                </div>
                                <div style="margin-bottom: 16px; display: flex; align-items: flex-start;">
                                    <span style="color: #000000; font-weight: 700; margin-right: 12px; font-size: 18px;">3.</span>
                                    <span style="color: #333333; font-size: 16px;">Start discovering deals with AI agents</span>
                                </div>
                            </div>
                        </div>

                        <!-- Features Overview -->
                        <div style="margin: 40px 0;">
                            <h3 style="color: #000000; font-size: 18px; font-weight: 600; margin: 0 0 24px 0; text-align: center;">
                                What you can do with TractionX:
                            </h3>
                            <div style="display: flex; flex-wrap: wrap; gap: 16px; justify-content: center;">
                                <div style="flex: 1; min-width: 150px; background: #ffffff; border: 2px solid #e5e5e5; border-radius: 12px; padding: 20px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">🤖</div>
                                    <h4 style="color: #000000; font-size: 14px; font-weight: 600; margin: 0 0 4px 0;">AI Deal Discovery</h4>
                                    <p style="color: #666666; font-size: 12px; margin: 0;">Autonomous agents find opportunities</p>
                                </div>
                                <div style="flex: 1; min-width: 150px; background: #ffffff; border: 2px solid #e5e5e5; border-radius: 12px; padding: 20px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">📊</div>
                                    <h4 style="color: #000000; font-size: 14px; font-weight: 600; margin: 0 0 4px 0;">Smart Analytics</h4>
                                    <p style="color: #666666; font-size: 12px; margin: 0;">Deep market intelligence & analysis</p>
                                </div>
                                <div style="flex: 1; min-width: 150px; background: #ffffff; border: 2px solid #e5e5e5; border-radius: 12px; padding: 20px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">⚡</div>
                                    <h4 style="color: #000000; font-size: 14px; font-weight: 600; margin: 0 0 4px 0;">Lightning Speed</h4>
                                    <p style="color: #666666; font-size: 12px; margin: 0;">10x faster deal flow management</p>
                                </div>
                            </div>
                        </div>

                        <!-- CTA Section -->
                        <div style="text-align: center; margin: 50px 0;">
                            <a href="https://v1.tractionx.ai/dashboard"
                            style="display: inline-block; background: #000000; color: #ffffff;
                                    padding: 18px 36px; text-decoration: none; border-radius: 12px;
                                    font-weight: 700; font-size: 18px; letter-spacing: -0.01em;
                                    border: 2px solid #000000;">
                                Explore Your Dashboard
                            </a>
                        </div>

                    </div>

                    <!-- Footer -->
                    <div style="background: #000000; padding: 30px 40px; text-align: center;">
                        <a href="https://www.tractionx.ai" style="color: #ffffff; text-decoration: none; font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">
                            Visit TractionX.ai
                        </a>
                        <p style="color: #cccccc; font-size: 12px; margin: 8px 0 0 0;">
                            Built with ❤️ for the future of investing
                        </p>
                    </div>

                </div>
            </body>
            </html>
            """

            await self._send_email(email, "Welcome to TractionX", html_content)
            logger.info(f"Welcome email sent to {email}")
        except Exception as e:
            logger.error(f"Failed to send welcome email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send welcome email",
            )

    async def send_organization_invitation(
        self, email: str, org_name: str, token: str
    ) -> None:
        """Send organization invitation email."""
        try:
            invite_url = f"{settings.FRONTEND_URL}/accept-org-invitation?token={token}"
            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Join {org_name} on TractionX</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                </style>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #ffffff;">
                
                <!-- Email Container -->
                <div style="width: 100%; max-width: 650px; margin: 0 auto; background: #ffffff;">
                    
                    <!-- Header Section with Logo -->
                    <div style="background: #000000; padding: 40px; text-align: center;">
                        <img src="https://www.tractionx.ai/logo_wo_wordmark_black.png" alt="TractionX Logo" style="height: 50px; width: auto; filter: invert(1); margin-bottom: 20px;">
                        <h1 style="color: #ffffff; font-size: 32px; font-weight: 700; margin: 0; letter-spacing: -0.02em;">
                            TractionX
                        </h1>
                        <p style="color: #ffffff; font-size: 16px; margin: 8px 0 0 0; opacity: 0.9;">
                            The First Agentic Operating System for Private Markets
                        </p>
                    </div>

                    <!-- Main Content -->
                    <div style="padding: 50px 40px; background: #ffffff;">
                        
                        <h2 style="color: #000000; font-size: 28px; font-weight: 700; margin: 0 0 24px 0; text-align: center; letter-spacing: -0.01em;">
                            Join {org_name} on TractionX
                        </h2>

                        <p style="color: #666666; font-size: 18px; margin: 0 0 32px 0; text-align: center; line-height: 1.6;">
                            You've been invited to join <strong style="color: #000000;">{org_name}</strong> on TractionX. Click the link below to accept the invitation and get started.
                        </p>

                        <!-- Organization Info -->
                        <div style="background: #f8f8f8; border-radius: 12px; padding: 24px; margin: 32px 0; text-align: center;">
                            <h3 style="color: #000000; font-size: 18px; font-weight: 600; margin: 0 0 12px 0;">
                                🏢 Organization
                            </h3>
                            <p style="color: #333333; font-size: 22px; font-weight: 700; margin: 0;">
                                {org_name}
                            </p>
                        </div>

                        <!-- CTA Section -->
                        <div style="text-align: center; margin: 50px 0;">
                            <a href="{invite_url}"
                               style="display: inline-block; background: #000000; color: #ffffff;
                                      padding: 18px 36px; text-decoration: none; border-radius: 12px;
                                      font-weight: 700; font-size: 18px; letter-spacing: -0.01em;
                                      border: 2px solid #000000;">
                                Accept Invitation
                            </a>
                            <p style="color: #666666; font-size: 14px; margin: 16px 0 0 0;">
                                This link will expire in {settings.INVITATION_TOKEN_EXPIRY // 3600} hours
                            </p>
                        </div>

                        <!-- Features Preview -->
                        <div style="background: #f8f8f8; border-radius: 12px; padding: 24px; margin: 32px 0;">
                            <h3 style="color: #000000; font-size: 16px; font-weight: 600; margin: 0 0 16px 0; text-align: center;">
                                What awaits you:
                            </h3>
                            <div style="display: flex; flex-wrap: wrap; gap: 16px; justify-content: center;">
                                <div style="flex: 1; min-width: 120px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">🤖</div>
                                    <p style="color: #333333; font-size: 12px; margin: 0; font-weight: 600;">AI-Powered Analysis</p>
                                </div>
                                <div style="flex: 1; min-width: 120px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">📈</div>
                                    <p style="color: #333333; font-size: 12px; margin: 0; font-weight: 600;">Deal Flow Management</p>
                                </div>
                                <div style="flex: 1; min-width: 120px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">⚡</div>
                                    <p style="color: #333333; font-size: 12px; margin: 0; font-weight: 600;">Real-time Insights</p>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Footer -->
                    <div style="background: #000000; padding: 30px 40px; text-align: center;">
                        <a href="https://www.tractionx.ai" style="color: #ffffff; text-decoration: none; font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">
                            Visit TractionX.ai
                        </a>
                        <p style="color: #cccccc; font-size: 12px; margin: 8px 0 0 0;">
                            Built with ❤️ for the future of investing
                        </p>
                    </div>

                </div>
            </body>
            </html>
            """

            await self._send_email(
                email, f"Invitation to Join {org_name}", html_content
            )
            logger.info(f"Organization invitation email sent to {email}")
        except Exception as e:
            logger.error(
                f"Failed to send organization invitation email to {email}: {str(e)}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send organization invitation email",
            )

    async def send_invite_code_email(
        self,
        email: str,
        invite_code: str,
        onboard_url: str,
        org_name: Optional[str] = None,
    ) -> bool:
        """Send invite code email to user."""
        try:
            subject = "🚀 Welcome to the Future of Private Markets Intelligence"

            # html_content = f"""
            # <!DOCTYPE html>
            #     <html lang="en">
            #     <head>
            #         <meta charset="UTF-8">
            #         <meta name="viewport" content="width=device-width, initial-scale=1.0">
            #         <title>Welcome to TractionX</title>
            #         <style>
            #             @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
            #         </style>
            #     </head>
            #     <body style="margin:0;padding:0;font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background:#fff;">

            #         <div style="width:100%;max-width:650px;margin:0 auto;background:#fff;box-shadow:0 6px 32px rgba(0,0,0,0.08);border-radius:24px;overflow:hidden;">

            #             <!-- Header with Logo and Tagline -->
            #             <div style="background:#000;padding:44px 0 32px 0;text-align:center;">
            #                 <img src="https://www.tractionx.ai/logo.png" alt="TractionX Logo" style="height:54px;width:auto;filter:invert(1);margin-bottom:20px;" />
            #                 <h1 style="color:#fff;font-size:32px;font-weight:700;margin:0;letter-spacing:-0.02em;">
            #                     TractionX Beta Access
            #                 </h1>
            #                 <p style="color:#fff;font-size:17px;margin:12px 0 0 0;opacity:0.89;letter-spacing:-0.01em;">
            #                     Access to private markets, the only place you need to be!
            #                 </p>
            #             </div>

            #             <div style="padding:50px 40px 44px 40px;background:#fff;">
            #                 <!-- Welcome Message -->
            #                 <div style="text-align:center;margin-bottom:36px;">
            #                     <h2 style="color:#000;font-size:27px;font-weight:700;margin:0 0 18px 0;letter-spacing:-0.01em;">
            #                         You're officially in the VIP lounge! 🥳
            #                     </h2>
            #                     <p style="color:#666;font-size:16px;margin:24px 0 0 0;line-height:1.6;">
            #                         We know private markets can be overwhelming and, let’s be real, a little messy. So we built TractionX to help you cut through the noise and actually have fun doing it.
            #                     </p>
            #                 </div>

            #                 <!-- Organization Info -->
            #                 <div style='background:#f8f8f8;border-radius:12px;padding:22px 18px 22px 22px;margin:30px 0 0 0;border-left:5px solid #000;'>
            #                         <h3 style='color:#000;font-size:16px;font-weight:600;margin:0 0 7px 0;'>
            #                             We're excited to have you on board!
            #                         </h3>
            #                         <p style='color:#222;font-size:19px;font-weight:600;margin:0;'>
            #                             {org_name}
            #                         </p>
            #                         <p style="color:#666;font-size:13px;margin:10px 0 0 0;">
            #                             You make this place way cooler.
            #                         </p>
            #                     </div>

            #                 <!-- Invite Code -->
            #                 <div style="background:#000;border-radius:17px;padding:32px 24px 26px 24px;text-align:center;margin:40px 0 0 0;box-shadow:0 4px 16px rgba(0,0,0,0.05);">
            #                     <h3 style="color:#fff;font-size:19px;font-weight:600;margin:0 0 16px 0;">
            #                         🔑 Your Magic Access Code
            #                     </h3>
            #                     <div style="background:#fff;border-radius:11px;padding:15px 0 11px 0;margin:15px 0;">
            #                         <code style="color:#000;font-family:'SF Mono',Monaco,'Cascadia Code',Consolas,monospace;font-size:25px;font-weight:700;letter-spacing:4px;">
            #                             {invite_code}
            #                         </code>
            #                     </div>
            #                     <p style="color:#bbb;font-size:15px;margin:8px 0 0 0;">
            #                         One use only, no take-backs!<br>
            #                         <span style="color:#888;font-size:13px;">(Expires in 30 days)</span>
            #                     </p>
            #                 </div>

            #                 <!-- CTA Button -->
            #                 <div style="text-align:center;margin:46px 0 0 0;">
            #                     <a href="{onboard_url}"
            #                     style="display:inline-block;background:#000;
            #                             color:#fff;text-decoration:none;padding:20px 46px;border-radius:13px;
            #                             font-weight:700;font-size:19px;letter-spacing:-0.01em;
            #                             border:2px solid #000;transition:background 0.2s;box-shadow:0 2px 14px rgba(0,0,0,0.04);">
            #                         Claim Your Spot 🚀
            #                     </a>
            #                     <p style="color:#888;font-size:15px;margin:14px 0 0 0;">
            #                         It takes less than 3 minutes (we timed it).
            #                     </p>
            #                 </div>

            #                 <!-- Social Proof / Fun Quote -->
            #                 <div style="background:#f8f8f8;border-radius:13px;padding:26px 18px;text-align:center;margin:40px 0 0 0;">
            #                     <p style="color:#222;font-size:15px;margin:0 0 8px 0;font-style:italic;">
            #                         “I used to lose track of deals in my inbox. Now I feel like I have an AI sidekick. TractionX just makes investing... way more fun.”
            #                     </p>
            #                     <p style="color:#999;font-size:13px;margin:0;font-weight:600;">
            #                         — Someone cool, probably
            #                     </p>
            #                 </div>
            #             </div>

            #             <!-- Footer with "Built with ❤️" -->
            #             <div style="background:#000;padding:30px 42px;text-align:center;border-bottom-left-radius:24px;border-bottom-right-radius:24px;">
            #                 <a href="https://www.tractionx.ai" style="color:#fff;text-decoration:none;font-size:15px;font-weight:600;margin-bottom:8px;display:block;">
            #                     Visit tractionx.ai
            #                 </a>
            #                 <p style="color:#fff;font-size:14px;margin:8px 0 0 0;font-weight:600;background:rgba(255,255,255,0.07);display:inline-block;padding:7px 18px;border-radius:10px;">
            #                     Built with ❤️ for the future of private markets
            #                 </p>
            #             </div>
            #         </div>
            #     </body>
            #     </html>
            # """
            html_content = f"""<!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>Welcome to TractionX</title>
                            <style>
                                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                            </style>
                        </head>
                        <body style="margin:0;padding:0;font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background:#fff;">
                            <div style="width:100%;max-width:650px;margin:0 auto;background:#fff;box-shadow:0 6px 32px rgba(0,0,0,0.08);border-radius:24px;overflow:hidden;">
                                <!-- Header with Logo and Tagline -->
                                <div style="background:#000;padding:44px 0 32px 0;text-align:center;">
                                    <img src="https://www.tractionx.ai/logo.png" alt="TractionX Logo" style="height:54px;width:auto;filter:invert(1);margin-bottom:20px;" />
                                    <h1 style="color:#fff;font-size:32px;font-weight:700;margin:0;letter-spacing:-0.02em;">
                                        TractionX Beta Access
                                    </h1>
                                    <p style="color:#fff;font-size:17px;margin:12px 0 0 0;opacity:0.89;letter-spacing:-0.01em;">
                                        Your Gateway to Smarter Private Market Investing
                                    </p>
                                </div>
                                <div style="padding:50px 40px 44px 40px;background:#fff;">
                                    <!-- Welcome Message -->
                                    <div style="text-align:center;margin-bottom:36px;">
                                        <h2 style="color:#000;font-size:27px;font-weight:700;margin:0 0 18px 0;letter-spacing:-0.01em;">
                                            Welcome to TractionX Beta
                                        </h2>
                                        <p style="color:#666;font-size:16px;margin:24px 0 0 0;line-height:1.6;">
                                            Private markets are complex and often chaotic. TractionX gives you clarity, structure, and the system to move with speed and confidence.
                                        </p>
                                    </div>
                                    <!-- Organization Info -->
                                    <div style='background:#f8f8f8;border-radius:12px;padding:22px 18px 22px 22px;margin:30px 0 0 0;border-left:5px solid #000;'>
                                        <h3 style='color:#000;font-size:16px;font-weight:600;margin:0 0 7px 0;'>
                                            Welcome to the future of private investing
                                        </h3>
                                        <p style='color:#222;font-size:19px;font-weight:600;margin:0;'>
                                            {org_name}
                                        </p>
                                        <p style="color:#666;font-size:13px;margin:10px 0 0 0;">
                                            Let's build something exceptional together.
                                        </p>
                                    </div>
                                    <!-- Invite Code -->
                                    <div style="background:#000;border-radius:17px;padding:32px 24px 26px 24px;text-align:center;margin:40px 0 0 0;box-shadow:0 4px 16px rgba(0,0,0,0.05);">
                                        <h3 style="color:#fff;font-size:19px;font-weight:600;margin:0 0 16px 0;">
                                            Your Access Code
                                        </h3>
                                        <div style="background:#fff;border-radius:11px;padding:15px 0 11px 0;margin:15px 0;">
                                            <code style="color:#000;font-family:'SF Mono',Monaco,'Cascadia Code',Consolas,monospace;font-size:25px;font-weight:700;letter-spacing:4px;">
                                                {invite_code}
                                            </code>
                                        </div>
                                        <p style="color:#bbb;font-size:15px;margin:8px 0 0 0;">
                                            One-time use only. Secure your access before it expires.<br>
                                            <span style="color:#888;font-size:13px;">(Expires in 30 days)</span>
                                        </p>
                                    </div>
                                    <!-- CTA Button -->
                                    <div style="text-align:center;margin:46px 0 0 0;">
                                        <a href="{onboard_url}"
                                        style="display:inline-block;background:#000;
                                                color:#fff;text-decoration:none;padding:20px 46px;border-radius:13px;
                                                font-weight:700;font-size:19px;letter-spacing:-0.01em;
                                                border:2px solid #000;transition:background 0.2s;box-shadow:0 2px 14px rgba(0,0,0,0.04);">
                                            Get Started with TractionX
                                        </a>
                                        <p style="color:#888;font-size:15px;margin:14px 0 10px 0;">
                                            Onboarding takes less than 3 minutes.
                                        </p>

                                        <!-- Onboarding Resources Link -->
                                        <a href="https://drive.google.com/file/d/1SlZO6vOX_sr8ZKoSLRAZcwHa4Bjpkfvo/view?usp=sharing" target="_blank">
                                            <img src="https://your-image-host.com/video-thumbnail.jpg" alt="Please watch the demo video for smooth onboarding!" style="width:100%;max-width:500px;border-radius:10px;">
                                        </a>
                                    </div>
                                    <!-- Social Proof / Quote -->
                                    <div style="background:#f8f8f8;border-radius:13px;padding:26px 18px;text-align:center;margin:40px 0 0 0;">
                                        <p style="color:#222;font-size:15px;margin:0 0 8px 0;font-style:italic;">
                                            "Even in its first version, TractionX has already improved how I work. it's faster, more organized, and simply more effective."
                                        </p>
                                        <p style="color:#999;font-size:13px;margin:0;font-weight:600;">
                                            — Beta User, VC Analyst
                                        </p>
                                    </div>
                                </div>
                                <!-- Footer -->
                                <div style="background:#000;padding:30px 42px;text-align:center;border-bottom-left-radius:24px;border-bottom-right-radius:24px;">
                                    <a href="https://www.tractionx.ai" style="color:#fff;text-decoration:none;font-size:15px;font-weight:600;margin-bottom:8px;display:block;">
                                        Visit tractionx.ai
                                    </a>
                                    <p style="color:#fff;font-size:14px;margin:8px 0 0 0;font-weight:600;background:rgba(255,255,255,0.07);display:inline-block;padding:7px 18px;border-radius:10px;">
                                        Built with purpose for the next generation of private market investing
                                    </p>
                                </div>
                            </div>
                        </body>
                        </html>"""

            await self._send_email(email, subject, html_content)
            logger.info(f"Invite code email sent to {email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send invite code email to {email}: {str(e)}")
            return False

    async def send_peer_invite_email(
        self,
        email: str,
        org_name: str,
        invited_by_name: str,
        invite_token: str,
        message: Optional[str] = None,
    ) -> bool:
        """Send peer invitation email."""
        try:
            accept_url = f"{settings.FRONTEND_URL}/accept-invite?token={invite_token}"
            subject = f"🎉 You've been invited to join {org_name} on TractionX"

            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Join {org_name} on TractionX</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                </style>
            </head>
            <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #ffffff;">
                
                <!-- Email Container -->
                <div style="width: 100%; max-width: 650px; margin: 0 auto; background: #ffffff;">
                    
                    <!-- Header Section with Logo -->
                    <div style="background: #000000; padding: 40px; text-align: center;">
                        <img src="https://www.tractionx.ai/logo_wo_wordmark_black.png" alt="TractionX Logo" style="height: 50px; width: auto; filter: invert(1); margin-bottom: 20px;">
                        <h1 style="color: #ffffff; font-size: 32px; font-weight: 700; margin: 0; letter-spacing: -0.02em;">
                            TractionX
                        </h1>
                        <p style="color: #ffffff; font-size: 16px; margin: 8px 0 0 0; opacity: 0.9;">
                            The First Agentic Operating System for Private Markets
                        </p>
                    </div>

                    <!-- Main Content -->
                    <div style="padding: 50px 40px; background: #ffffff;">
                        
                        <h2 style="color: #000000; font-size: 28px; font-weight: 700; margin: 0 0 24px 0; text-align: center; letter-spacing: -0.01em;">
                            Join {org_name} on TractionX
                        </h2>

                        <p style="color: #666666; font-size: 18px; margin: 0 0 24px 0; text-align: center; line-height: 1.6;">
                            <strong style="color: #000000;">{invited_by_name}</strong> has invited you to join their team.
                        </p>

                        {f'<div style="background: #f8f8f8; border-radius: 12px; padding: 24px; margin: 32px 0; border-left: 4px solid #000000;"><p style="font-size: 16px; color: #333333; margin: 0; font-style: italic;">"{message}"</p></div>' if message else ""}

                        <!-- Organization Info -->
                        <div style="background: #f8f8f8; border-radius: 12px; padding: 24px; margin: 32px 0; text-align: center;">
                            <h3 style="color: #000000; font-size: 18px; font-weight: 600; margin: 0 0 12px 0;">
                                🏢 You're joining
                            </h3>
                            <p style="color: #333333; font-size: 22px; font-weight: 700; margin: 0;">
                                {org_name}
                            </p>
                        </div>

                        <!-- CTA Section -->
                        <div style="text-align: center; margin: 50px 0;">
                            <a href="{accept_url}"
                               style="display: inline-block; background: #000000; color: #ffffff;
                                      padding: 18px 36px; text-decoration: none; border-radius: 12px;
                                      font-weight: 700; font-size: 18px; letter-spacing: -0.01em;
                                      border: 2px solid #000000;">
                                Accept Invitation
                            </a>
                            <p style="color: #666666; font-size: 14px; margin: 16px 0 0 0;">
                                This invitation will expire in 7 days
                            </p>
                        </div>

                        <!-- What to Expect -->
                        <div style="background: #f8f8f8; border-radius: 12px; padding: 24px; margin: 32px 0;">
                            <h3 style="color: #000000; font-size: 16px; font-weight: 600; margin: 0 0 16px 0; text-align: center;">
                                What you'll get access to:
                            </h3>
                            <div style="display: flex; flex-wrap: wrap; gap: 16px; justify-content: center;">
                                <div style="flex: 1; min-width: 120px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">🤖</div>
                                    <p style="color: #333333; font-size: 12px; margin: 0; font-weight: 600;">AI Deal Discovery</p>
                                </div>
                                <div style="flex: 1; min-width: 120px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">📊</div>
                                    <p style="color: #333333; font-size: 12px; margin: 0; font-weight: 600;">Market Intelligence</p>
                                </div>
                                <div style="flex: 1; min-width: 120px; text-align: center;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">⚡</div>
                                    <p style="color: #333333; font-size: 12px; margin: 0; font-weight: 600;">Lightning Analytics</p>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Footer -->
                    <div style="background: #000000; padding: 30px 40px; text-align: center;">
                        <a href="https://www.tractionx.ai" style="color: #ffffff; text-decoration: none; font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">
                            Visit TractionX.ai
                        </a>
                        <p style="color: #cccccc; font-size: 12px; margin: 8px 0 0 0;">
                            Built with ❤️ for the future of investing
                        </p>
                    </div>

                </div>
            </body>
            </html>
            """

            await self._send_email(email, subject, html_content)
            logger.info(f"Peer invite email sent to {email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send peer invite email to {email}: {str(e)}")
            return False
