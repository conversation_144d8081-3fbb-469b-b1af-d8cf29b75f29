from datetime import datetime
from typing import Optional

from bson import ObjectId
from fastapi import Depends, <PERSON><PERSON>, HTTPException, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.auth_exceptions import (
    InactiveUserError,
    InvalidTokenError,
    MissingTokenError,
    TokenExpiredError,
    TokenTypeError,
    UserNotFoundError,
)
from app.core.config import settings
from app.core.database import get_database
from app.core.logging import get_logger
from app.models.token import Token, TokenType
from app.models.user import PublicUser, User
from app.services.auth.interface import IAuthService
from app.services.factory import get_auth_service

logger = get_logger(__name__)

# Define public paths that don't require authentication

security = HTTPBearer()


async def get_current_user(
    authorization: Optional[str] = Header(None),
    auth_service: IAuthService = Depends(get_auth_service),
    request: Request = None,  # type: ignore
) -> User:
    """Get current user from JWT token."""
    logger.info(f"Get current user called with: {authorization}")

    # Check if the current path is public
    if (request and request.url.path in (settings.PUBLIC_PATHS)) or (
        request.url.path.startswith("/api/v1/forms/share/")
        or request.url.path.startswith("/api/v1/magic-link")
        or request.url.path.startswith("/api/v1/public/")
        or request.url.path.startswith("/api/v1/accept-invitation")
        or request.url.path.startswith("/api/v1/magic-link/verify")
        or request.url.path.startswith("/api/v1/reset-password")
        or request.url.path.startswith("/api/v1/forgot-password")
        or request.url.path.startswith("/api/v1/auth/public")
        or request.url.path.startswith("/api/v1/onboard")
    ):
        logger.info(f"Skipping auth for public path: {request.url.path}")
        # Return a dummy user for public paths
        dummy_id = ObjectId("000000000000000000000000")
        return User(
            id=dummy_id,
            name="Public User",
            email="<EMAIL>",
            password_hash="",
            provider="public",
            org_id=dummy_id,
            org_ids=[dummy_id],
            role_id=None,
            status="active",
            is_superuser=False,
            is_active=True,
        )

    # Short-circuit if pass_through is enabled for this org
    if request is not None and getattr(request.state, "pass_through", False):
        dummy_id = ObjectId("000000000000000000000000")
        return User(
            id=dummy_id,
            name="System User",
            email="<EMAIL>",
            password_hash="",
            provider="system",
            org_id=dummy_id,
            org_ids=[dummy_id],
            role_id=None,
            status="active",
            is_superuser=True,
            is_active=True,
        )

    # For non-public paths, require authorization
    if not authorization:
        raise MissingTokenError()

    if authorization and authorization.startswith("Bearer "):
        authorization = authorization.split(" ")[1]

    try:
        # Verify token
        token = await auth_service.verify_token(authorization, TokenType.ACCESS)
        if not token:
            raise InvalidTokenError()

        # Check if this is a public user token
        if token.metadata and token.metadata.get("user_type") == "public":
            # Get public user
            public_user = await PublicUser.find_one({"_id": token.sub})
            if not public_user:
                raise UserNotFoundError(user_id=str(token.sub))

            # Check if public user is active
            if not public_user.is_active:
                raise InactiveUserError(user_id=str(public_user.id))

            # Convert PublicUser to User format for compatibility
            # This is a temporary solution to maintain backward compatibility
            dummy_id = ObjectId("000000000000000000000000")
            return User(
                id=public_user.id,
                name=public_user.name or "Public User",
                email=public_user.email,
                password_hash="",
                provider="public",
                org_id=dummy_id,
                org_ids=[dummy_id],
                role_id=None,
                status="active",
                is_superuser=False,
                is_active=True,
            )
        else:
            # Get regular user
            user = await User.find_one({"_id": token.sub})
            if not user:
                raise UserNotFoundError(user_id=str(token.sub))

            # Check if user is active
            if user.status != "active":
                raise InactiveUserError(user_id=str(user.id))

    except (
        TokenExpiredError,
        InvalidTokenError,
        TokenTypeError,
        MissingTokenError,
        UserNotFoundError,
        InactiveUserError,
    ) as auth_error:
        # Log the error
        logger.warning(f"Auth error in dependency: {auth_error.detail}")
        # Our custom exceptions already have the correct status codes and details
        raise auth_error
    except Exception as e:
        # Check if it's a token-related exception
        error_message = str(e)
        if "token" in error_message.lower() and "expire" in error_message.lower():
            logger.warning(f"Token expired exception: {error_message}")
            raise TokenExpiredError()
        # Re-raise other exceptions
        raise

    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """Get current active user."""
    if current_user.status != "active":
        raise InactiveUserError(user_id=str(current_user.id))
    return current_user


async def get_current_public_user(
    authorization: Optional[str] = Header(None),
    auth_service: IAuthService = Depends(get_auth_service),
    request: Request = None,
) -> dict:
    """Get current public user from JWT token."""
    logger.info(f"Get current public user called with: {authorization}")

    # For non-public paths, require authorization
    if not authorization:
        raise MissingTokenError()

    if authorization and authorization.startswith("Bearer "):
        authorization = authorization.split(" ")[1]

    try:
        # Verify token
        # token = await auth_service.verify_token(authorization, TokenType.ACCESS)
        # if not token:
        #     raise InvalidTokenError()
        now = int(datetime.utcnow().timestamp())
        token = await Token.find_one(
            query={
                "token": authorization,
                "type": TokenType.ACCESS,
                "is_revoked": False,
                "exp": {"$gt": now},
            }
        )

        # Check if this is a public user token
        if token.metadata and token.metadata.get("user_type") == "public":
            # Get public user
            public_user = await PublicUser.find_one({"_id": token.sub})
            if not public_user:
                raise UserNotFoundError(user_id=str(token.sub))

            # Check if public user is active
            if not public_user.is_active:
                raise InactiveUserError(user_id=str(public_user.id))

            # Return public user data as dict
            return {
                "id": str(public_user.id),
                "email": public_user.email,
                "name": public_user.name or "Public User",
                "type": "public",
                "user_id": str(public_user.id),  # For backwards compatibility
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid token type for public user access",
            )

    except (
        TokenExpiredError,
        InvalidTokenError,
        TokenTypeError,
        MissingTokenError,
        UserNotFoundError,
        InactiveUserError,
    ) as auth_error:
        # Log the error
        logger.warning(f"Public user auth error in dependency: {auth_error.detail}")
        raise auth_error
    except Exception as e:
        logger.error(f"Unexpected error in get_current_public_user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed",
        )


async def check_permission(
    resource: str,
    action: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> bool:
    """Check if current user has permission for resource and action."""
    from app.services.rbac.mongo import RBACService

    rbac_service = RBACService(db)
    has_permission = await rbac_service.check_permission(
        current_user.id, resource, action
    )

    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
        )

    return True


async def validate_org_header(
    x_org_id: str = Header(..., description="Organization ID header"),
    db: AsyncIOMotorDatabase = Depends(get_database),
    current_user: User = Depends(get_current_user),
    request: Request = None,
) -> str:
    # Short-circuit if pass_through is enabled for this org
    if request is not None and getattr(request.state, "pass_through", False):
        return "dummy-org-id"
    """Validate organization header and check user access."""
    if not ObjectId.is_valid(x_org_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid organization ID format",
        )

    # Check if organization exists
    org = await db.organizations.find_one({"_id": ObjectId(x_org_id)})
    if not org:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
        )
    logger.info(f"ORG123: {org}")
    logger.info(f"CURRENT USER123: {current_user}")
    # Check if user has access to this organization
    if not current_user.is_superuser and current_user.id not in org.get("user_ids", []):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have access to this organization",
        )

    return x_org_id
