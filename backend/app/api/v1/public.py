"""
Public API endpoints for shared form submission flow.

This module handles the investor-token shared submission flow:
1. Public user login with email
2. Magic link generation and verification
3. Submission tracking by token + public user
4. Draft/submitted status management
"""

from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel, EmailStr

from app.core.logging import get_logger
from app.models.form import Submission
from app.services.auth.interface import IAuthService
from app.services.factory import (
    get_auth_service,
    get_public_submission_service,
    get_public_user_service,
    get_sharing_service,
)
from app.services.public_submission.service import PublicSubmissionService
from app.services.sharing.interfaces import SharingServiceInterface
from app.services.user.interfaces import PublicUserServiceInterface

logger = get_logger(__name__)

router = APIRouter(prefix="/public", tags=["Public"])


class PublicLoginRequest(BaseModel):
    """Request model for public user login."""

    email: EmailStr
    name: Optional[str] = None
    token: str  # The sharing token


class PublicLoginResponse(BaseModel):
    """Response model for public user login."""

    message: str
    email: str
    requires_verification: bool = True


class MagicLinkVerifyResponse(BaseModel):
    """Response model for magic link verification."""

    access_token: str
    refresh_token: str
    user: Dict[str, Any]
    submission: Optional[Dict[str, Any]] = None
    can_edit: bool = True
    redirect_url: Optional[str] = None


class MagicVerifyRequest(BaseModel):
    """Request model for magic link verification."""

    token: str


class SubmissionProgressRequest(BaseModel):
    """Request model for saving submission progress."""

    answers: Dict[str, Any]
    progress: float
    last_question: Optional[str] = None


@router.post("/login", response_model=PublicLoginResponse)
async def public_login(
    request: PublicLoginRequest,
    http_request: Request,
    sharing_service: SharingServiceInterface = Depends(get_sharing_service),
    public_user_service: PublicUserServiceInterface = Depends(get_public_user_service),
    auth_service: IAuthService = Depends(get_auth_service),
    public_submission_service: PublicSubmissionService = Depends(
        get_public_submission_service
    ),
) -> PublicLoginResponse:
    """
    Public user login endpoint.

    Validates the sharing token, creates/gets public user, and sends magic link.
    """
    try:
        # Validate sharing token
        sharing_data = await sharing_service.validate_sharing_token(request.token)
        if not sharing_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid or expired sharing link",
            )

        # Extract form and organization data
        form_data = sharing_data.get("resource_data")
        org_data = sharing_data.get("organization")

        if not form_data or not org_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Form or organization not found",
            )

        # Get or create public user
        public_user = await public_user_service.get_public_user_by_email(request.email)
        if not public_user:
            public_user = await public_user_service.create_public_user(
                email=request.email,
                name=request.name,
                metadata={"created_via": "shared_form", "sharing_token": request.token},
            )
            logger.info(f"Created new public user: {public_user.email}")
        else:
            # Update last access
            await public_user_service.update_last_access(str(public_user.id))
            logger.info(f"Found existing public user: {public_user.email}")

        # Track form access
        await public_user_service.track_form_access(
            str(public_user.id), str(form_data["_id"])
        )

        # Get or create public submission tracking
        token_expires_at = None
        if "expires_at" in sharing_data:
            token_expires_at = sharing_data["expires_at"]

        submission = await public_submission_service.get_or_create_submission(
            public_user_id=str(public_user.id),
            public_user_email=public_user.email,
            sharing_token=request.token,
            form_id=str(form_data["_id"]),
            org_id=str(org_data["_id"]),
            token_expires_at=token_expires_at,
        )

        # Generate magic link token
        magic_token = await auth_service.create_magic_link_token(
            user_id=str(public_user.id),
            email=public_user.email,
            redirect_url=f"/share/{request.token}",
        )

        # Send magic link email using auth service (handles redirect URL properly)
        await auth_service.send_magic_link_email(
            email=public_user.email,
            token=magic_token,
            redirect_url=f"/share/{request.token}",
        )

        # Track access
        await public_submission_service.track_access(
            public_submission_id=str(submission.id) if submission else "",
            public_user_email=public_user.email,
            sharing_token=request.token,
            access_type="login",
            success=True,
            ip_address=http_request.client.host if http_request.client else None,
            user_agent=http_request.headers.get("user-agent"),
        )

        return PublicLoginResponse(
            message="Magic link sent to your email",
            email=public_user.email,
            requires_verification=True,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Public login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Login failed"
        )


@router.post("/magic-link/verify", response_model=MagicLinkVerifyResponse)
async def verify_magic_link(
    magic_verify_request: MagicVerifyRequest,
    auth_service: IAuthService = Depends(get_auth_service),
    public_user_service: PublicUserServiceInterface = Depends(get_public_user_service),
    public_submission_service: PublicSubmissionService = Depends(
        get_public_submission_service
    ),
) -> MagicLinkVerifyResponse:
    """
    Verify magic link and return authentication tokens.
    """
    try:
        token = magic_verify_request.token
        # Verify magic link token
        token_data = await auth_service.verify_magic_link_token(token)
        if not token_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired magic link",
            )

        user_id = token_data["user_id"]
        email = token_data["email"]

        # Get public user
        public_user = await public_user_service.get_public_user(user_id)
        if not public_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # Create authentication tokens
        auth_tokens = await auth_service.create_public_user_tokens(public_user)

        # Get the specific submission for this sharing token (from redirect_url)
        redirect_url = token_data.get("redirect_url")
        sharing_token = None
        if redirect_url and redirect_url.startswith("/share/"):
            sharing_token = redirect_url.split("/share/")[1]

        current_submission = None
        if sharing_token:
            current_submission = (
                await public_submission_service.get_submission_by_token_and_email(
                    sharing_token, email
                )
            )
            logger.info(
                f"Found submission for token {sharing_token}: {current_submission.id if current_submission else 'None'}"
            )
        else:
            # Fallback: get the most recent submission
            submissions = await public_submission_service.list_submissions_by_user(
                email, limit=1
            )
            current_submission = submissions[0] if submissions else None
            logger.info(
                f"Fallback: Found {len(submissions) if submissions else 0} submissions"
            )

        return MagicLinkVerifyResponse(
            access_token=auth_tokens.access_token,
            refresh_token=auth_tokens.refresh_token,
            user={
                "id": str(public_user.id),
                "email": public_user.email,
                "name": public_user.name,
                "type": "public",
            },
            submission=(
                {
                    "id": str(current_submission.id),
                    "status": current_submission.status,
                    "progress": current_submission.progress_percentage,
                    "can_edit": current_submission.can_edit(),
                    "answers": current_submission.answers,
                    "last_question": current_submission.last_question_answered,
                }
                if current_submission
                else None
            ),
            can_edit=current_submission.can_edit() if current_submission else True,
            redirect_url=token_data.get("redirect_url"),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Magic link verification error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired magic link",
        )


@router.get("/submission/{token}")
async def get_submission_by_token(
    token: str,
    email: str,
    public_submission_service: PublicSubmissionService = Depends(
        get_public_submission_service
    ),
) -> Dict[str, Any]:
    """
    Get submission status by token and email.
    """
    try:
        validation = await public_submission_service.validate_submission_access(
            token, email
        )

        if not validation.get("valid"):  # type: ignore
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=validation["reason"],  # type: ignore
            )

        submission = validation["submission"]  # type: ignore
        return {
            "id": str(submission.id),
            "status": submission.status,
            "progress": submission.progress_percentage,
            "can_edit": submission.can_edit(),
            "answers": submission.answers,
            "last_question": submission.last_question_answered,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get submission error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get submission",
        )


@router.post("/submission/{submission_id}/progress")
async def save_submission_progress(
    submission_id: str,
    request: SubmissionProgressRequest,
    public_submission_service: PublicSubmissionService = Depends(
        get_public_submission_service
    ),
) -> Dict[str, Any]:
    """
    Save submission progress.
    """
    try:
        submission = await public_submission_service.update_submission_progress(
            submission_id=submission_id,
            answers=request.answers,
            progress=request.progress,
            last_question=request.last_question,
        )

        if not submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Submission not found or cannot be edited",
            )

        return {
            "id": str(submission.id),
            "status": submission.status,
            "progress": submission.progress_percentage,
            "message": "Progress saved successfully",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Save progress error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save progress",
        )


@router.post("/submission/{submission_id}/submit")
async def submit_public_submission(
    submission_id: str,
    sharing_service: SharingServiceInterface = Depends(get_sharing_service),
    public_submission_service: PublicSubmissionService = Depends(
        get_public_submission_service
    ),
) -> Dict[str, Any]:
    """
    Submit a public submission (convert from DRAFT to SUBMITTED).
    """
    try:
        # Get the public submission
        public_submission = await public_submission_service.get_submission_by_id(
            submission_id
        )
        if not public_submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Submission not found"
            )

        if not public_submission.can_edit():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Submission cannot be edited",
            )

        # Validate sharing token is still valid
        sharing_data = await sharing_service.validate_sharing_token(
            public_submission.sharing_token
        )
        if not sharing_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid or expired sharing link",
            )

        # Create the actual submission
        submission = await Submission(
            org_id=public_submission.org_id,
            form_id=public_submission.form_id,
            answers=public_submission.answers,
            metadata={
                "submitted_via": "shared_form",
                "sharing_token": public_submission.sharing_token,
                "public_user_email": public_submission.public_user_email,
                "public_submission_id": str(public_submission.id),
            },
        ).save()

        # Update the public submission to mark as submitted
        await public_submission_service.finalize_submission(
            public_submission_id=submission_id, final_submission_id=str(submission.id)
        )

        # Create submission processing job
        from datetime import datetime

        from app.utils.jobs.job_utils import create_job_chain

        jobs = await create_job_chain(
            entity_type="form_submission",
            entity_id=str(submission.id),
            job_configs=[
                # Submission processing job (new comprehensive workflow)
                {
                    "job_type": "submission_processing",
                    "queue_job_type": "submission_processing",
                    "payload": {
                        "submission_id": str(submission.id),
                        "form_id": str(public_submission.form_id),
                        "org_id": str(public_submission.org_id),
                        "answers": public_submission.answers,
                        "metadata": {
                            "submitted_via": "shared_form",
                            "sharing_token": public_submission.sharing_token,
                            "public_user_email": public_submission.public_user_email,
                            "public_submission_id": str(public_submission.id),
                            "submission_time": int(datetime.now().timestamp()),
                        },
                        "created_by": None,  # Public submission, no user context
                    },
                    "metadata": {
                        "submission_time": int(datetime.now().timestamp()),
                        "submission_source": "public_shared_form",
                    },
                }
            ],
        )
        logger.info(
            f"Created submission processing job chain for public submission {submission.id}: {jobs}"
        )

        return {
            "id": str(submission.id),
            "status": "submitted",
            "message": "Submission completed successfully",
            "public_submission_id": submission_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Submit public submission error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to submit"
        )
