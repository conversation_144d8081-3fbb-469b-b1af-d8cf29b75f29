"""
Onboarding API endpoints for invite code generation and organization setup.

This module handles the viral onboarding flow:
1. Invite code generation
2. Organization creation
3. User profile setup
4. Peer invitations

This module provides endpoints for creating default forms and theses for new organizations.
"""

from typing import Any, Dict

from fastapi import Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user, get_database
from app.dependencies.org import get_org_context
from app.models.auth import TokenData
from app.schemas.auth import (
    CreateInviteCodeRequest,
    CreateInviteCodeResponse,
    OnboardingCompleteResponse,
    OnboardingStepOneRequest,
    OnboardingStepTwoRequest,
    PeerInviteRequest,
)
from app.services.factory import (
    get_form_service,
    get_onboarding_service,
    get_role_service,
    get_thesis_service,
)
from app.services.form.interfaces import FormServiceInterface
from app.services.onboarding.interface import IOnboardingService
from app.services.thesis.interfaces import ThesisServiceInterface
from app.utils.common import PyObjectId

logger = get_logger(__name__)

# Public router for invite code generation and onboarding
public_router = BaseAPIRouter(
    prefix="", disable_auth=True, require_org=False, tags=["onboarding"]
)

# Protected router for peer invites
protected_router = BaseAPIRouter(prefix="", tags=["onboarding"])

router = BaseAPIRouter(prefix="/onboarding", tags=["onboarding"])


@public_router.post(
    "/auth/public/create-invite-code", response_model=CreateInviteCodeResponse
)
async def create_invite_code(
    request: CreateInviteCodeRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Generate a unique invite code for organization onboarding.

    This endpoint creates a single-use invite code that allows the specified
    email to create a new organization and user account.
    """
    try:
        logger.info(f"Creating invite code for {request.email}")

        invite_code = await onboarding_service.create_invite_code(
            email=request.email, org_name=request.org_name
        )

        # Construct the onboarding URL
        base_url = "https://v1.tractionx.ai"
        onboard_url = f"{base_url}/onboard/{invite_code.code}"

        # Send the invite email
        from app.services.factory import get_email_service

        email_service = await get_email_service()

        await email_service.send_invite_code_email(
            email=invite_code.email,
            invite_code=invite_code.code,
            onboard_url=onboard_url,
            org_name=invite_code.org_name,
        )

        return CreateInviteCodeResponse(
            code=invite_code.code,
            email=invite_code.email,
            expires_at=invite_code.expires_at,
            onboard_url=onboard_url,
        )

    except Exception as e:
        logger.error(f"Error creating invite code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create invite code",
        )


@public_router.get("/onboard/{code}/validate")
async def validate_invite_code(
    code: str,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Validate an invite code and return associated information.

    This endpoint checks if an invite code is valid and returns
    the associated email and organization name (if any).
    """
    try:
        invite_code = await onboarding_service.validate_invite_code(code)

        return {
            "valid": True,
            "email": invite_code.email,
            "org_name": invite_code.org_name,
            "expires_at": invite_code.expires_at,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating invite code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate invite code",
        )


@public_router.post("/onboard/{code}/step-1")
async def onboarding_step_one(
    code: str,
    request: OnboardingStepOneRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Step 1: Create organization during onboarding.

    This endpoint creates the organization with the provided details
    and validates the subdomain availability.
    """
    try:
        logger.info(f"Onboarding step 1 for code {code}")

        # Validate invite code
        invite_code = await onboarding_service.validate_invite_code(code)

        # Create organization
        organization = await onboarding_service.create_organization(
            invite_code=invite_code,
            org_name=request.org_name,
            subdomain=request.subdomain,
            website=request.website,
            logo_url=request.logo_url,
        )
        role_service = await get_role_service()
        gp_role, analyst_role = await role_service.create_default_roles(  # type: ignore
            str(organization.id)
        )

        return {
            "success": True,
            "org_id": str(organization.id),
            "message": "Organization created successfully",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in onboarding step 1: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization",
        )


@public_router.post("/onboard/{code}/step-2", response_model=OnboardingCompleteResponse)
async def onboarding_step_two(
    code: str,
    request: OnboardingStepTwoRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Step 2: Create user profile and complete onboarding.

    This endpoint creates the user profile, completes the onboarding
    process, and returns authentication tokens.
    """
    try:
        logger.info(f"Onboarding step 2 for code {code}")

        # Validate invite code
        invite_code = await onboarding_service.validate_invite_code(code)

        # Get the organization created in step 1
        from app.models.organization import Organization

        organization = await Organization.find_one({"contact_email": invite_code.email})

        if not organization:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organization not found. Please complete step 1 first.",
            )

        # Create user profile
        user = await onboarding_service.create_user_profile(
            invite_code=invite_code,
            organization=organization,
            name=request.name,
            password=request.password,
            profile_picture_url=request.profile_picture_url,
        )

        # Complete onboarding and get tokens
        result = await onboarding_service.complete_onboarding(
            invite_code=invite_code, user=user, organization=organization
        )

        return OnboardingCompleteResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in onboarding step 2: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete onboarding",
        )


@public_router.get("/onboard/check-subdomain/{subdomain}")
async def check_subdomain_availability(
    subdomain: str,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Check if a subdomain is available for use.

    This endpoint validates subdomain format and checks availability.
    """
    try:
        available = await onboarding_service.check_subdomain_availability(subdomain)

        return {"subdomain": subdomain, "available": available}

    except Exception as e:
        logger.error(f"Error checking subdomain availability: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check subdomain availability",
        )


@protected_router.post("/auth/public/invite-peers")
async def invite_peers(
    request: PeerInviteRequest,
    current_user: TokenData = Depends(get_current_user),
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Send invitations to peers to join the organization.

    This endpoint allows users to invite colleagues to their organization
    after completing the onboarding process.
    """
    try:
        logger.info(f"Sending peer invites from user {current_user.user_id}")

        result = await onboarding_service.send_peer_invites(
            org_id=current_user.org_id,
            invited_by=current_user.user_id,
            emails=request.emails,
            role_id=request.role_id,
            message=request.message,
        )

        return result

    except Exception as e:
        logger.error(f"Error sending peer invites: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send peer invites",
        )


@router.post("/create-default-form", response_model=Dict[str, Any])
async def create_default_form(
    org_context: tuple[str, bool] = Depends(get_org_context),
    form_service: FormServiceInterface = Depends(get_form_service),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> Dict[str, Any]:
    """
    Create a default form for the organization.

    This endpoint creates a comprehensive demo form that demonstrates all available
    question types, visibility conditions, and validation rules.

    Returns:
        Dictionary containing the created form details
    """
    try:
        org_id = org_context[0]

        # Create default form
        default_form = await form_service.create_default_form(PyObjectId(org_id))

        if not default_form:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create default form",
            )

        logger.info(
            f"Created default form {default_form.get('_id')} for organization {org_id}"
        )

        return {
            "success": True,
            "message": "Default form created successfully",
            "form": default_form,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating default form: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create default form: {str(e)}",
        )


@router.post("/create-demo-thesis", response_model=Dict[str, Any])
async def create_demo_thesis(
    form_id: str,
    org_context: tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> Dict[str, Any]:
    """
    Create a demo thesis for the specified form.

    This endpoint creates a comprehensive demo thesis that demonstrates all available
    scoring features including match rules, scoring rules, bonus rules, and exclusion filters.

    Args:
        form_id: ID of the form to create thesis for

    Returns:
        Dictionary containing the created thesis details
    """
    try:
        org_id = org_context[0]

        # Create demo thesis
        demo_thesis = await thesis_service.create_demo_thesis(
            PyObjectId(org_id), form_id
        )

        if not demo_thesis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create demo thesis",
            )

        logger.info(f"Created demo thesis {demo_thesis.id} for organization {org_id}")

        return {
            "success": True,
            "message": "Demo thesis created successfully",
            "thesis": {
                "id": str(demo_thesis.id),
                "name": demo_thesis.name,
                "description": demo_thesis.description,
                "status": demo_thesis.status,
                "is_active": demo_thesis.is_active,
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating demo thesis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create demo thesis: {str(e)}",
        )


@router.post("/setup-organization", response_model=Dict[str, Any])
async def setup_organization(
    org_context: tuple[str, bool] = Depends(get_org_context),
    form_service: FormServiceInterface = Depends(get_form_service),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> Dict[str, Any]:
    """
    Complete organization setup - creates both default form and demo thesis.

    This endpoint is the main onboarding endpoint that creates everything needed
    for a new organization to start using the platform effectively.

    Returns:
        Dictionary containing the created form and thesis details
    """
    try:
        org_id = org_context[0]

        # Step 1: Create default form
        logger.info(f"Creating default form for organization {org_id}")
        default_form = await form_service.create_default_form(PyObjectId(org_id))

        if not default_form:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create default form",
            )

        form_id = default_form.get("_id")
        logger.info(f"Created default form {form_id}")

        # Step 2: Create demo thesis
        logger.info(f"Creating demo thesis for form {form_id}")
        demo_thesis = await thesis_service.create_demo_thesis(
            PyObjectId(org_id), str(form_id)
        )

        if not demo_thesis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create demo thesis",
            )

        logger.info(f"Created demo thesis {demo_thesis.id}")

        return {
            "success": True,
            "message": "Organization setup completed successfully",
            "form": {
                "id": str(form_id),
                "name": default_form.get("name"),
                "description": default_form.get("description"),
                "sections": len(default_form.get("sections", [])),
                "default_sections": len(default_form.get("default_section_ids", [])),
            },
            "thesis": {
                "id": str(demo_thesis.id),
                "name": demo_thesis.name,
                "description": demo_thesis.description,
                "status": demo_thesis.status,
                "is_active": demo_thesis.is_active,
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting up organization: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to setup organization: {str(e)}",
        )
