from typing import Dict, Any, List, Optional
from fastapi import Depends, HTTPException, status, Query
from pydantic import BaseModel, <PERSON>, model_validator

from app.services.factory import get_qualifier_form_service, get_form_service
from app.models.answer import AnswerSchema
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.api.base import BaseAPIRouter
from app.utils.rbac.rbac import rbac_register
from app.models.user import User
from app.services.sharing.interfaces import SharingType, EmbedType
from app.services.trigger.interfaces import TriggerType
from app.services.form.interfaces import FormServiceInterface

router = BaseAPIRouter(prefix="/qualifier-forms", tags=["qualifier-forms"])


class CreateQualifierFormRequest(BaseModel):
    name: str = Field(..., description="Name of the form")
    description: str = Field(..., description="Description of the form")
    sections: Optional[List[str]] = Field(None, description="List of section IDs")
    default_section_ids: Optional[List[str]] = Field(None, description="List of default section IDs")
    is_active: bool = Field(True, description="Whether the form is active")


class UpdateQualifierFormRequest(BaseModel):
    name: Optional[str] = Field(None, description="Name of the form")
    description: Optional[str] = Field(None, description="Description of the form")
    sections: Optional[List[str]] = Field(None, description="List of section IDs")
    default_section_ids: Optional[List[str]] = Field(None, description="List of default section IDs")
    is_active: Optional[bool] = Field(None, description="Whether the form is active")


class CreateSectionRequest(BaseModel):
    title: str = Field(..., min_length=1, max_length=100, description="Title of the section")
    description: str = Field(..., min_length=1, max_length=500, description="Description of the section")
    order: int = Field(..., ge=0, description="Order of the section in the form")
    repeatable: bool = Field(False, description="Whether the section can be repeated")


class UpdateSectionRequest(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=100, description="Title of the section")
    description: Optional[str] = Field(None, min_length=1, max_length=500, description="Description of the section")
    order: Optional[int] = Field(None, ge=0, description="Order of the section in the form")
    repeatable: Optional[bool] = Field(None, description="Whether the section can be repeated")


class CreateQuestionRequest(BaseModel):
    type: str = Field(..., min_length=1, max_length=50, description="Type of question (text, select, etc.)")
    label: str = Field(..., min_length=1, max_length=200, description="Label/question text")
    help_text: str = Field(..., min_length=1, max_length=500, description="Help text for the question")
    required: bool = Field(False, description="Whether the question is required")
    options: Optional[List[Dict[str, str]]] = Field(None, description="Options for select questions")
    validation: Optional[Dict[str, Any]] = Field(None, description="Validation rules")
    repeat_section_id: Optional[str] = Field(None, description="ID of a section to repeat based on this question's answer")
    max_repeats: Optional[int] = Field(None, ge=1, le=100, description="Maximum number of repeats allowed for the embedded section")
    visibility_condition: Optional[Dict[str, Any]] = Field(None, description="Visibility conditions")
    order: int = Field(0, ge=0, description="Order of the question in the section")


class UpdateQuestionRequest(BaseModel):
    type: Optional[str] = Field(None, min_length=1, max_length=50, description="Type of question (text, select, etc.)")
    label: Optional[str] = Field(None, min_length=1, max_length=200, description="Label/question text")
    help_text: Optional[str] = Field(None, min_length=1, max_length=500, description="Help text for the question")
    required: Optional[bool] = Field(None, description="Whether the question is required")
    options: Optional[List[Dict[str, str]]] = Field(None, description="Options for select questions")
    validation: Optional[Dict[str, Any]] = Field(None, description="Validation rules")
    visibility_condition: Optional[Dict[str, Any]] = Field(None, description="Visibility conditions")
    order: Optional[int] = Field(None, ge=0, description="Order of the question in the section")


class SharingConfigRequest(BaseModel):
    enabled: bool = Field(True, description="Whether sharing is enabled")
    sharing_types: List[SharingType] = Field(
        default=[SharingType.LINK],
        description="List of sharing types to enable"
    )
    allowed_domains: Optional[List[str]] = Field(
        None,
        description="List of domains allowed for embedding"
    )
    embed_type: EmbedType = Field(
        default=EmbedType.INLINE,
        description="Type of embed"
    )
    custom_styles: Optional[Dict[str, Any]] = Field(
        None,
        description="Custom styles for embedding"
    )
    tracking_enabled: bool = Field(
        True,
        description="Whether to track views/usage"
    )
    expires_at: Optional[int] = Field(
        None,
        description="When the sharing configuration expires (unix timestamp)"
    )


class TriggerConfigRequest(BaseModel):
    id: Optional[str] = Field(None, description="ID of existing trigger config to update")
    type: TriggerType = Field(..., description="Type of trigger")
    name: str = Field(..., description="Name of the trigger")
    description: Optional[str] = Field(None, description="Description of the trigger")
    enabled: bool = Field(True, description="Whether the trigger is enabled")
    config: Dict[str, Any] = Field(default_factory=dict, description="Type-specific configuration")
    conditions: Dict[str, Any] = Field(default_factory=dict, description="Conditions for when to execute")
    retry_config: Optional[Dict[str, Any]] = Field(None, description="Retry configuration")


class SubmitQualifierFormRequest(BaseModel):
    """
    Request model for submitting a qualifier form.

    Supports two formats for answers:
    1. Flat structure (backward compatible): question_id -> answer
    2. Structured format with repeatable sections

    The API will handle both formats appropriately.
    """
    # For backward compatibility, accept a flat dictionary of answers
    answers: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Flat dictionary of answers (question_id -> answer)"
    )

    # For structured submissions with repeatable sections
    answer_schema: Optional[AnswerSchema] = Field(
        default=None,
        description="Structured answer schema with repeatable sections"
    )

    # Optional metadata
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional metadata for the submission"
    )

    @model_validator(mode='after')
    def validate_answers_provided(self) -> 'SubmitQualifierFormRequest':
        """Ensure that either answers or answer_schema is provided."""
        if self.answers is None and self.answer_schema is None:
            raise ValueError("Either answers or answer_schema must be provided")
        return self


@router.post("", status_code=status.HTTP_201_CREATED)
@rbac_register(resource="qualifier_forms", action="create", group="Qualifier Forms", description="Create qualifier form")
async def create_qualifier_form(
    request: CreateQualifierFormRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> Dict[str, Any]:
    """
    Create a new qualifier form.

    The organization ID is automatically extracted from the X-ORG-ID header.

    ## Workflow for Creating a Complete Qualifier Form:

    1. Create a qualifier form using this endpoint
    2. Add sections to the form using POST /{form_id}/sections
    3. Add questions to the sections using POST /sections/{section_id}/questions

    ## Available Operations:

    ### Form Operations:
    - GET /{form_id} - Get a form
    - PUT /{form_id} - Update a form
    - DELETE /{form_id} - Delete a form

    ### Section Operations:
    - POST /{form_id}/sections - Create a section
    - GET /sections/{section_id} - Get a section
    - PATCH /sections/{section_id} - Update a section
    - DELETE /sections/{section_id} - Delete a section

    ### Question Operations:
    - POST /sections/{section_id}/questions - Create a question
    - GET /questions/{question_id} - Get a question
    - PATCH /questions/{question_id} - Update a question
    - DELETE /questions/{question_id} - Delete a question

    Sections are automatically added to the form when created, and questions are
    automatically added to sections when created. You don't need to update the form
    or section separately after adding sections or questions.
    """
    org_id, is_cross_org = org_context

    try:
        form = await qualifier_form_service.create_qualifier_form(
            name=request.name,
            description=request.description,
            org_id=org_id,  # org_id is automatically extracted from X-ORG-ID header
            sections=request.sections,
            default_section_ids=request.default_section_ids,
            is_active=request.is_active
        )
        return form
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create qualifier form: {str(e)}"
        )


@router.get("/{form_id}")
@rbac_register(resource="qualifier_forms", action="view", group="Qualifier Forms", description="Get qualifier form")
async def get_qualifier_form(
    form_id: str,
    details: bool = False,
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> Dict[str, Any]:
    """
    Get qualifier form by ID.
    """
    if details:
        form = await qualifier_form_service.get_qualifier_form_with_details(form_id)
    else:
        form = await qualifier_form_service.get_qualifier_form(form_id)

    if not form:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Qualifier form not found"
        )
    return form


@router.put("/{form_id}")
@rbac_register(resource="qualifier_forms", action="update", group="Qualifier Forms", description="Update qualifier form")
async def update_qualifier_form(
    form_id: str,
    request: UpdateQualifierFormRequest,
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> Dict[str, Any]:
    """
    Update a qualifier form.
    """
    # Filter out None values
    updates = {k: v for k, v in request.model_dump().items() if v is not None}

    if not updates:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No updates provided"
        )

    form = await qualifier_form_service.update_qualifier_form(form_id, updates)
    if not form:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Qualifier form not found"
        )
    return form


@router.delete("/{form_id}")
@rbac_register(resource="qualifier_forms", action="delete", group="Qualifier Forms", description="Delete qualifier form")
async def delete_qualifier_form(
    form_id: str,
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> Dict[str, str]:
    """
    Delete a qualifier form.
    """
    success = await qualifier_form_service.delete_qualifier_form(form_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Qualifier form not found"
        )
    return {"status": "Qualifier form deleted"}


@router.get("")
@rbac_register(resource="qualifier_forms", action="view", group="Qualifier Forms", description="List qualifier forms")
async def list_qualifier_forms(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> List[Dict[str, Any]]:
    """
    List qualifier forms for an organization.

    The organization ID is automatically extracted from the X-ORG-ID header.
    """
    org_id, is_cross_org = org_context  # org_id is automatically extracted from X-ORG-ID header

    return await qualifier_form_service.list_qualifier_forms(
        org_id=org_id,
        skip=skip,
        limit=limit,
        search=search,
        is_active=is_active
    )


@router.post("/{form_id}/sections", status_code=status.HTTP_201_CREATED)
@rbac_register(resource="qualifier_forms", action="update", group="Qualifier Forms", description="Add section to form")
async def add_section_to_form(
    form_id: str,
    request: CreateSectionRequest,
    form_service: FormServiceInterface = Depends(get_form_service),
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> Dict[str, Any]:
    """
    Add a new section to a qualifier form.

    After creating the section, you can add questions to it using the
    POST /forms/sections/{section_id}/questions endpoint.
    """
    try:
        # First check if the form exists
        form = await qualifier_form_service.get_qualifier_form(form_id)
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Qualifier form not found"
            )

        # Create the section
        section = await form_service.create_section(
            form_id=form_id,
            title=request.title,
            description=request.description,
            order=request.order,
            questions=[],  # Start with empty questions list
            repeatable=request.repeatable
        )

        # The section is automatically added to the form by the create_section method

        # Return the section with its ID
        return {
            "id": str(section.id),
            "form_id": str(section.form_id),
            "title": section.title,
            "description": section.description,
            "order": section.order,
            "questions": [],
            "repeatable": section.repeatable,
            "created_at": section.created_at,
            "updated_at": section.updated_at
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add section to form: {str(e)}"
        )


@router.post("/sections/{section_id}/questions", status_code=status.HTTP_201_CREATED)
@rbac_register(resource="qualifier_forms", action="update", group="Qualifier Forms", description="Add question to section")
async def add_question_to_section(
    section_id: str,
    request: CreateQuestionRequest,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Dict[str, Any]:
    """
    Add a new question to a section.

    The question will be automatically added to the section, and you don't need to update
    the section separately.
    """
    try:
        # Create the question
        question = await form_service.create_question(
            section_id=section_id,
            type=request.type,
            label=request.label,
            help_text=request.help_text,
            required=request.required,
            options=request.options,
            validation=request.validation,
            visibility_condition=request.visibility_condition,
            repeat_section_id=request.repeat_section_id,
            max_repeats=request.max_repeats,
            order=request.order
        )

        # The question is automatically added to the section by the create_question method

        # Return the question with its ID
        return {
            "id": str(question["_id"]),
            "section_id": str(question["section_id"]),
            "type": question["type"],
            "label": question["label"],
            "help_text": question["help_text"],
            "required": question["required"],
            "options": question["options"],
            "validation": question["validation"],
            "visibility_condition": question["visibility_condition"],
            "repeat_section_id": str(question["repeat_section_id"]) if question.get("repeat_section_id") else None,
            "max_repeats": question.get("max_repeats"),
            "order": question["order"],
            "created_at": question["created_at"],
            "updated_at": question["updated_at"]
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add question to section: {str(e)}"
        )


@router.get("/sections/{section_id}")
@rbac_register(resource="qualifier_forms", action="view", group="Qualifier Forms", description="Get section")
async def get_section(
    section_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Dict[str, Any]:
    """
    Get a section by ID.

    This endpoint returns the section with its basic information. To get the section with its questions,
    use the `details=true` query parameter.
    """
    try:
        # Get the section with questions
        section_data = await form_service.get_section_with_questions(section_id)
        if not section_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Section not found"
            )

        # Convert ObjectId to string for JSON serialization
        section_data["_id"] = str(section_data["_id"])
        section_data["form_id"] = str(section_data["form_id"])

        # Convert question ObjectIds to strings
        questions = []
        for question in section_data.get("questions", []):
            question["_id"] = str(question["_id"])
            question["section_id"] = str(question["section_id"])
            questions.append(question)

        section_data["questions"] = questions

        return section_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get section: {str(e)}"
        )


@router.patch("/sections/{section_id}")
@rbac_register(resource="qualifier_forms", action="update", group="Qualifier Forms", description="Update section")
async def update_section(
    section_id: str,
    request: UpdateSectionRequest,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Dict[str, Any]:
    """
    Update a section.
    """
    try:
        # Filter out None values
        updates = {k: v for k, v in request.model_dump().items() if v is not None}

        if not updates:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No updates provided"
            )

        section = await form_service.update_section(section_id, updates)
        if not section:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Section not found"
            )

        return {
            "id": str(section.id),
            "form_id": str(section.form_id),
            "title": section.title,
            "description": section.description,
            "order": section.order,
            "repeatable": section.repeatable,
            "created_at": section.created_at,
            "updated_at": section.updated_at
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update section: {str(e)}"
        )


@router.delete("/sections/{section_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(resource="qualifier_forms", action="delete", group="Qualifier Forms", description="Delete section")
async def delete_section(
    section_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """
    Delete a section.

    The section will be automatically removed from its form, and all questions in the section will also be deleted.
    """
    try:
        success = await form_service.delete_section(section_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Section not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete section: {str(e)}"
        )


@router.get("/questions/{question_id}")
@rbac_register(resource="qualifier_forms", action="view", group="Qualifier Forms", description="Get question")
async def get_question(
    question_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Dict[str, Any]:
    """
    Get a question by ID.
    """
    try:
        question = await form_service.get_question(question_id)
        if not question:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Question not found"
            )

        return {
            "id": str(question.id),
            "section_id": str(question.section_id),
            "type": question.type,
            "label": question.label,
            "help_text": question.help_text,
            "required": question.required,
            "options": question.options,
            "validation": question.validation,
            "visibility_condition": question.visibility_condition,
            "repeat_section_id": str(question.repeat_section_id) if question.repeat_section_id else None,
            "max_repeats": question.max_repeats,
            "order": question.order,
            "created_at": question.created_at,
            "updated_at": question.updated_at
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get question: {str(e)}"
        )


@router.patch("/questions/{question_id}")
@rbac_register(resource="qualifier_forms", action="update", group="Qualifier Forms", description="Update question")
async def update_question(
    question_id: str,
    request: UpdateQuestionRequest,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Dict[str, Any]:
    """
    Update a question.
    """
    try:
        # Filter out None values
        updates = {k: v for k, v in request.model_dump().items() if v is not None}

        if not updates:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No updates provided"
            )

        question = await form_service.update_question(question_id, updates)
        if not question:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Question not found"
            )

        return {
            "id": str(question.id),
            "section_id": str(question.section_id),
            "type": question.type,
            "label": question.label,
            "help_text": question.help_text,
            "required": question.required,
            "options": question.options,
            "validation": question.validation,
            "visibility_condition": question.visibility_condition,
            "repeat_section_id": str(question.repeat_section_id) if question.repeat_section_id else None,
            "max_repeats": question.max_repeats,
            "order": question.order,
            "created_at": question.created_at,
            "updated_at": question.updated_at
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update question: {str(e)}"
        )


@router.delete("/questions/{question_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(resource="qualifier_forms", action="delete", group="Qualifier Forms", description="Delete question")
async def delete_question(
    question_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """
    Delete a question.

    The question will be automatically removed from its section.
    """
    try:
        success = await form_service.delete_question(question_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Question not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete question: {str(e)}"
        )


@router.post("/{form_id}/sharing")
@rbac_register(resource="qualifier_forms", action="update", group="Qualifier Forms", description="Configure sharing")
async def configure_sharing(
    form_id: str,
    request: SharingConfigRequest,
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> Dict[str, Any]:
    """
    Configure sharing for a qualifier form.
    """
    try:
        form = await qualifier_form_service.configure_sharing(
            form_id=form_id,
            sharing_config=request.model_dump()
        )
        return form
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to configure sharing: {str(e)}"
        )


@router.post("/{form_id}/triggers")
@rbac_register(resource="qualifier_forms", action="update", group="Qualifier Forms", description="Configure triggers")
async def configure_triggers(
    form_id: str,
    request: List[TriggerConfigRequest],
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> Dict[str, Any]:
    """
    Configure triggers for a qualifier form.
    """
    try:
        form = await qualifier_form_service.configure_triggers(
            form_id=form_id,
            trigger_configs=[trigger.model_dump() for trigger in request]
        )
        return form
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to configure triggers: {str(e)}"
        )


@router.get("/{form_id}/stats")
@rbac_register(resource="qualifier_forms", action="view", group="Qualifier Forms", description="Get form statistics")
async def get_qualifier_form_stats(
    form_id: str,
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> Dict[str, Any]:
    """
    Get statistics for a qualifier form.
    """
    try:
        stats = await qualifier_form_service.get_qualifier_form_stats(form_id)
        return stats
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get form statistics: {str(e)}"
        )


@router.post("/{form_id}/submit")
@rbac_register(resource="qualifier_forms", action="submit", group="Qualifier Forms", description="Submit form")
async def submit_qualifier_form(
    form_id: str,
    request: SubmitQualifierFormRequest,
    qualifier_form_service = Depends(get_qualifier_form_service),
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Dict[str, Any]:
    """
    Submit a qualifier form.

    This endpoint supports two formats for answers:
    1. Flat structure (backward compatible): question_id -> answer
    2. Structured format with repeatable sections

    Example payload with structured format:
    ```json
    {
      "answer_schema": {
        "answers": {
          "681e02498dddb89bb46eed76": "Acme Technologies",
          "681e02598dddb89bb46eed79": "https://acmetech.example.com",
          "681e041db468fc6230c70bc9": 2,
          "681e044db468fc6230c70bcc": "other",
          "681e052f58f07bc4951e5000": "AI-powered Manufacturing"
        },
        "repeatable_sections": [
          {
            "section_id": "681e01fd8dddb89bb46eed71",
            "controller_question_id": "681e041db468fc6230c70bc9",
            "instances": [
              {
                "instance_id": "6820a1b3f5e8d7c9b2a1e0f4",
                "answers": {
                  "681e07c558f07bc4951e5032": ["pre_seed", "angel"]
                }
              },
              {
                "instance_id": "6820a1b3f5e8d7c9b2a1e0f5",
                "answers": {
                  "681e07c558f07bc4951e5032": ["seed", "grants"]
                }
              }
            ]
          }
        ]
      },
      "metadata": {
        "submitted_at": 1746799000,
        "user_agent": "Mozilla/5.0",
        "ip_address": "127.0.0.1"
      }
    }
    ```

    Example payload with flat format (backward compatible):
    ```json
    {
      "answers": {
        "681e02498dddb89bb46eed76": "Acme Technologies",
        "681e02598dddb89bb46eed79": "https://acmetech.example.com",
        "681e041db468fc6230c70bc9": 2,
        "681e01fd8dddb89bb46eed71_0_681e07c558f07bc4951e5032": ["pre_seed", "angel"],
        "681e01fd8dddb89bb46eed71_1_681e07c558f07bc4951e5032": ["seed", "grants"]
      },
      "metadata": {
        "submitted_at": 1746799000,
        "user_agent": "Mozilla/5.0",
        "ip_address": "127.0.0.1"
      }
    }
    ```
    """
    try:
        # Get the form to retrieve repeatable section information
        form_details = await form_service.get_form_with_details(form_id)
        if not form_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Form not found"
            )

        # Process answers based on the provided format
        answers_to_submit = {}

        if request.answer_schema:
            # Use the structured answer schema
            answers_to_submit = request.answer_schema.to_flat_dict()
        elif request.answers:
            # Use the flat dictionary of answers (backward compatibility)
            answers_to_submit = request.answers
        else:
            # This should never happen due to the model validator
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No answers provided"
            )

        # Submit the form with the processed answers and metadata
        submission = await qualifier_form_service.submit_qualifier_form(
            form_id=form_id,
            answers=answers_to_submit,
            metadata=request.metadata
        )
        return submission
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit form: {str(e)}"
        )


@router.get("/{form_id}/submissions")
@rbac_register(resource="qualifier_forms", action="view", group="Qualifier Forms", description="Get form submissions")
async def get_qualifier_form_submissions(
    form_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    qualifier_form_service = Depends(get_qualifier_form_service)
) -> List[Dict[str, Any]]:
    """
    Get submissions for a qualifier form.
    """
    try:
        submissions = await qualifier_form_service.get_qualifier_form_submissions(
            form_id=form_id,
            skip=skip,
            limit=limit
        )
        return submissions
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get form submissions: {str(e)}"
        )
