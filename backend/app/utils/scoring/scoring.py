"""
Scoring Utility Functions

This module provides utility functions for scoring matches between expected values
and actual values for different data types, as well as evaluating conditions.
"""

from typing import Any, List, Union, Dict
import re
from difflib import SequenceMatcher


def score_number(expected: Union[int, float], actual: Union[int, float]) -> float:
    """
    Score a numeric match between expected and actual values.
    
    The score is calculated based on the relative difference between the values.
    A perfect match (expected == actual) returns 1.0.
    As the difference increases, the score approaches 0.0.
    
    Args:
        expected: Expected numeric value
        actual: Actual numeric value
        
    Returns:
        Score between 0.0 and 1.0
    """
    if expected == actual:
        return 1.0
    
    # Calculate relative difference
    max_val = max(abs(expected), abs(actual))
    if max_val == 0:
        return 1.0  # Both are zero
    
    diff = abs(expected - actual)
    relative_diff = diff / max_val
    
    # Convert to score (1.0 for perfect match, approaching 0.0 for large differences)
    score = 1.0 / (1.0 + relative_diff)
    
    return score


def score_boolean(expected: bool, actual: bool) -> float:
    """
    Score a boolean match between expected and actual values.
    
    Args:
        expected: Expected boolean value
        actual: Actual boolean value
        
    Returns:
        1.0 if values match, 0.0 otherwise
    """
    return 1.0 if expected == actual else 0.0


def score_short_text(expected: str, actual: str) -> float:
    """
    Score a text match between expected and actual values.
    
    Uses SequenceMatcher to calculate similarity between strings.
    
    Args:
        expected: Expected text value
        actual: Actual text value
        
    Returns:
        Score between 0.0 and 1.0
    """
    # Normalize strings
    expected_norm = expected.lower().strip()
    actual_norm = actual.lower().strip()
    
    if expected_norm == actual_norm:
        return 1.0
    
    # Calculate similarity ratio
    similarity = SequenceMatcher(None, expected_norm, actual_norm).ratio()
    
    return similarity


def score_long_text(expected: str, actual: str) -> float:
    """
    Score a long text match between expected and actual values.
    
    For long text, we focus on key terms and semantic similarity.
    
    Args:
        expected: Expected text value
        actual: Actual text value
        
    Returns:
        Score between 0.0 and 1.0
    """
    # Normalize strings
    expected_norm = expected.lower().strip()
    actual_norm = actual.lower().strip()
    
    if expected_norm == actual_norm:
        return 1.0
    
    # Extract key terms from expected text
    expected_terms = set(re.findall(r'\b\w+\b', expected_norm))
    if not expected_terms:
        return 0.0
    
    # Count how many expected terms appear in actual text
    actual_terms = set(re.findall(r'\b\w+\b', actual_norm))
    matching_terms = expected_terms.intersection(actual_terms)
    
    # Calculate score based on term overlap
    score = len(matching_terms) / len(expected_terms)
    
    return score


def score_multi_select(expected: List[Any], actual: List[Any]) -> float:
    """
    Score a multi-select match between expected and actual values.
    
    The score is based on the overlap between the two lists.
    
    Args:
        expected: Expected list of values
        actual: Actual list of values
        
    Returns:
        Score between 0.0 and 1.0
    """
    if not expected:
        return 1.0 if not actual else 0.0
    
    if not actual:
        return 0.0
    
    # Convert to sets for easier intersection
    expected_set = set(expected)
    actual_set = set(actual)
    
    # Calculate Jaccard similarity: |A ∩ B| / |A ∪ B|
    intersection = len(expected_set.intersection(actual_set))
    union = len(expected_set.union(actual_set))
    
    if union == 0:
        return 1.0  # Both sets are empty
    
    return intersection / union


def score_single_select(expected: Any, actual: Any) -> float:
    """
    Score a single-select match between expected and actual values.
    
    Args:
        expected: Expected value
        actual: Actual value
        
    Returns:
        1.0 if values match, 0.0 otherwise
    """
    return 1.0 if expected == actual else 0.0


def score_range(expected: List[Union[int, float]], actual: Union[int, float]) -> float:
    """
    Score a range match between expected range and actual value.
    
    Args:
        expected: Expected range as [min, max]
        actual: Actual value
        
    Returns:
        Score between 0.0 and 1.0
    """
    if len(expected) != 2:
        return 0.0
    
    min_val, max_val = expected
    
    # Check if actual is within range
    if min_val <= actual <= max_val:
        return 1.0
    
    # Calculate distance to range
    if actual < min_val:
        distance = min_val - actual
        reference = max(abs(min_val), abs(max_val))
    else:  # actual > max_val
        distance = actual - max_val
        reference = max(abs(min_val), abs(max_val))
    
    if reference == 0:
        return 0.0
    
    # Convert to score (approaching 0.0 as distance increases)
    relative_distance = distance / reference
    score = 1.0 / (1.0 + relative_distance)
    
    return score


def evaluate_condition(condition: Dict[str, Any], value: Any) -> bool:
    """
    Evaluate a condition against a value.
    
    Args:
        condition: Dictionary with operator and value, e.g., {"eq": 10}
        value: Value to evaluate against
        
    Returns:
        True if condition is met, False otherwise
    """
    if not condition:
        return True
        
    operator = next(iter(condition))
    condition_value = condition[operator]
    
    if operator == "eq":
        return value == condition_value
    elif operator == "ne":
        return value != condition_value
    elif operator == "gt":
        return value > condition_value
    elif operator == "lt":
        return value < condition_value
    elif operator == "gte":
        return value >= condition_value
    elif operator == "lte":
        return value <= condition_value
    elif operator == "contains":
        if isinstance(value, str) and isinstance(condition_value, str):
            return condition_value in value
        elif isinstance(value, list):
            return condition_value in value
        return False
    elif operator == "not_contains":
        if isinstance(value, str) and isinstance(condition_value, str):
            return condition_value not in value
        elif isinstance(value, list):
            return condition_value not in value
        return True
    elif operator == "starts_with":
        return isinstance(value, str) and value.startswith(condition_value)
    elif operator == "ends_with":
        return isinstance(value, str) and value.endswith(condition_value)
    elif operator == "in":
        return value in condition_value if isinstance(condition_value, list) else False
    elif operator == "not_in":
        return value not in condition_value if isinstance(condition_value, list) else True
    elif operator == "between":
        if isinstance(condition_value, list) and len(condition_value) == 2:
            return condition_value[0] <= value <= condition_value[1]
        return False
    elif operator == "not_between":
        if isinstance(condition_value, list) and len(condition_value) == 2:
            return value < condition_value[0] or value > condition_value[1]
        return True
    else:
        return False


def score_match(expected: Any, actual: Any, match_type: str = "auto") -> float:
    """
    Score a match between expected and actual values based on their types.
    
    Args:
        expected: Expected value
        actual: Actual value
        match_type: Type of match to perform (auto, number, boolean, text, multi_select, single_select, range)
        
    Returns:
        Score between 0.0 and 1.0
    """
    # Auto-detect match type if not specified
    if match_type == "auto":
        if isinstance(expected, (int, float)) and isinstance(actual, (int, float)):
            match_type = "number"
        elif isinstance(expected, bool) and isinstance(actual, bool):
            match_type = "boolean"
        elif isinstance(expected, str) and isinstance(actual, str):
            if len(expected) > 100 or len(actual) > 100:
                match_type = "long_text"
            else:
                match_type = "short_text"
        elif isinstance(expected, list) and isinstance(actual, list):
            match_type = "multi_select"
        elif isinstance(expected, list) and len(expected) == 2 and isinstance(actual, (int, float)):
            match_type = "range"
        else:
            match_type = "single_select"
    
    # Score based on match type
    if match_type == "number":
        return score_number(expected, actual)
    elif match_type == "boolean":
        return score_boolean(expected, actual)
    elif match_type == "short_text":
        return score_short_text(expected, actual)
    elif match_type == "long_text":
        return score_long_text(expected, actual)
    elif match_type == "multi_select":
        return score_multi_select(expected, actual)
    elif match_type == "single_select":
        return score_single_select(expected, actual)
    elif match_type == "range":
        return score_range(expected, actual)
    else:
        # Default to exact match
        return 1.0 if expected == actual else 0.0


