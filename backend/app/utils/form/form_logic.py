"""
Form Logic Utilities

This module provides utilities for evaluating form logic, including visibility conditions
and dependency management for advanced form features like conditional visibility and
repeatable section embedding.
"""

from typing import Dict, Any, List, Optional, Union
from collections import defaultdict
import operator

from app.models.form import VisibilityCondition, ConditionClause, Question


# Operator mapping for condition evaluation
OPERATOR_MAP = {
    "==": operator.eq,
    "!=": operator.ne,
    ">": operator.gt,
    "<": operator.lt,
    ">=": operator.ge,
    "<=": operator.le,
}


def evaluate_visibility(
    answers: Dict[str, Any],
    condition: Union[VisibilityCondition, Dict[str, Any]],
    instance_index_map: Optional[Dict[str, int]] = None
) -> bool:
    """
    Evaluate a visibility condition against the current answers.

    Args:
        answers: Dictionary of question_id -> answer or structured format with answers/repeatable_answers
        condition: The visibility condition to evaluate
        instance_index_map: Optional mapping of section_id -> instance_index for scoped targeting

    Returns:
        True if the condition is met (question should be visible), False otherwise
    """
    if not condition:
        return True

    # Handle dict input for backward compatibility
    if isinstance(condition, dict):
        operator_str = condition.get("operator", "==")
        conditions_list = condition.get("conditions", [])
    else:
        # Handle Pydantic model
        operator_str = getattr(condition, "operator", "==")
        conditions_list = getattr(condition, "conditions", [])

    # Initialize instance_index_map if not provided
    if instance_index_map is None:
        instance_index_map = {}

    # Convert structured answers to flat format if needed
    flat_answers = answers
    if isinstance(answers, dict) and 'answers' in answers and 'repeatable_answers' in answers:
        # Create a flat dictionary for visibility evaluation
        flat_answers = answers['answers'].copy()

        # Add repeatable section answers
        for section_id, instances in answers['repeatable_answers'].items():
            for instance_id, instance_answers in instances.items():
                try:
                    instance_idx = int(instance_id)
                except ValueError:
                    instance_idx = 0

                for question_id, answer in instance_answers.items():
                    # Format 1: section_id_instance_id_question_id
                    key1 = f"{section_id}_{instance_id}_{question_id}"
                    flat_answers[key1] = answer

                    # Format 2: question_id__instance_idx
                    key2 = f"{question_id}__{instance_idx}"
                    flat_answers[key2] = answer

    # Define resolver for individual clauses
    def resolve_clause(clause: Union[Dict[str, Any], ConditionClause]) -> bool:
        # Handle dict input for backward compatibility
        if isinstance(clause, dict):
            question_id = str(clause.get("question_id", ""))
            expected_value = clause.get("value")
            section_instance_index = clause.get("section_instance_index")
        else:
            question_id = str(clause.question_id)
            expected_value = clause.value
            section_instance_index = clause.section_instance_index

        # Apply scoping for repeated sections
        scoped_id = question_id
        if section_instance_index is not None:
            scoped_id = f"{question_id}__{section_instance_index}"
        elif question_id in instance_index_map:
            # Use the current instance index from the map if available
            scoped_id = f"{question_id}__{instance_index_map[question_id]}"

        # Get the answer value from the flat answers
        answer_value = flat_answers.get(scoped_id)

        # If the answer doesn't exist yet, return False for visibility
        # This handles the case of the first question in "next" mode
        if answer_value is None:
            # For the first question, we should return True if there are no answers yet
            if len(flat_answers) == 0 and scoped_id == question_id:
                return True
            return False

        # Handle different operators
        if operator_str in OPERATOR_MAP:
            # Use the appropriate comparison operator
            return OPERATOR_MAP[operator_str](answer_value, expected_value)

        # Default to equality for backward compatibility
        return answer_value == expected_value

    # Evaluate based on the logical operator
    if operator_str == "and":
        return all(resolve_clause(c) for c in conditions_list)
    elif operator_str == "or":
        return any(resolve_clause(c) for c in conditions_list)
    elif operator_str == "not":
        # For 'not', we negate the result of the first condition
        if conditions_list:
            return not resolve_clause(conditions_list[0])
        return True
    else:
        # For comparison operators, evaluate each condition
        return all(resolve_clause(c) for c in conditions_list)


def build_visibility_graph(questions: List[Union[Question, Dict[str, Any]]]) -> Dict[str, List[str]]:
    """
    Build a dependency graph for question visibility.

    Args:
        questions: List of Question objects or dictionaries

    Returns:
        A graph represented as a dictionary where keys are question IDs and values are
        lists of question IDs that depend on them
    """
    graph = defaultdict(list)

    for question in questions:
        # Handle both Question objects and dictionaries
        if isinstance(question, dict):
            question_id = str(question.get("_id", ""))
            visibility_condition = question.get("visibility_condition")
        else:
            question_id = str(question.id)
            visibility_condition = question.visibility_condition

        if not visibility_condition:
            continue

        # Handle both VisibilityCondition objects and dictionaries
        if isinstance(visibility_condition, dict):
            conditions = visibility_condition.get("conditions", [])
        else:
            # Handle Pydantic model
            conditions = getattr(visibility_condition, "conditions", [])

        for condition in conditions:
            # Handle both ConditionClause objects and dictionaries
            if isinstance(condition, dict):
                dependency_id = str(condition.get("question_id", ""))
            else:
                dependency_id = str(condition.question_id)

            if dependency_id:
                graph[dependency_id].append(question_id)

    return dict(graph)


def detect_cycles(graph: Dict[str, List[str]]) -> List[List[str]]:
    """
    Detect cycles in the visibility dependency graph.

    Args:
        graph: A graph represented as a dictionary where keys are question IDs and values are
              lists of question IDs that depend on them

    Returns:
        A list of cycles, where each cycle is a list of question IDs
    """
    visited = set()
    stack = set()
    path = []
    cycles = []

    def visit(node):
        if node in stack:
            # Found a cycle
            cycle_start = path.index(node)
            cycles.append(path[cycle_start:] + [node])
            return
        if node in visited:
            return

        visited.add(node)
        stack.add(node)
        path.append(node)

        for neighbor in graph.get(node, []):
            visit(neighbor)

        path.pop()
        stack.remove(node)

    # Visit all nodes
    for node in graph:
        if node not in visited:
            visit(node)

    return cycles


def validate_visibility_references(
    questions: List[Union[Question, Dict[str, Any]]],
    section_map: Dict[str, Any]
) -> List[str]:
    """
    Validate that all question references in visibility conditions exist
    and that repeatable sections are properly configured.

    Args:
        questions: List of Question objects or dictionaries
        section_map: Dictionary mapping section IDs to section objects

    Returns:
        A list of error messages, empty if no errors
    """
    errors = []

    # Create a set of all question IDs for quick lookup
    question_ids = set()
    for q in questions:
        if isinstance(q, dict):
            q_id = q.get("_id") or q.get("id")
            if q_id:
                question_ids.add(str(q_id))
        else:
            if hasattr(q, 'id') and q.id:
                question_ids.add(str(q.id))
            elif hasattr(q, '_id') and q._id:
                question_ids.add(str(q._id))

    # Create a set of all section IDs for quick lookup
    section_ids = set(section_map.keys())

    # Track repeatable sections for additional validation
    repeatable_sections = {}
    for section_id, section in section_map.items():
        if isinstance(section, dict):
            if section.get("repeatable"):
                repeatable_sections[section_id] = section
        else:
            if getattr(section, "repeatable", False):
                repeatable_sections[section_id] = section

    # Track repeatable section controllers for validation
    repeatable_controllers = {}

    for question in questions:
        # Extract question properties, handling both objects and dictionaries
        if isinstance(question, dict):
            question_id = str(question.get("_id") or question.get("id", ""))
            question_label = question.get("label", "Unknown")
            question_type = question.get("type", "Unknown")
            visibility_condition = question.get("visibility_condition")
            repeat_section_id = question.get("repeat_section_id")
            max_repeats = question.get("max_repeats")
            section_id = question.get("section_id")
        else:
            question_id = str(getattr(question, "id", "") or getattr(question, "_id", ""))
            question_label = getattr(question, "label", "Unknown")
            question_type = getattr(question, "type", "Unknown")
            visibility_condition = getattr(question, "visibility_condition", None)
            repeat_section_id = getattr(question, "repeat_section_id", None)
            max_repeats = getattr(question, "max_repeats", None)
            section_id = getattr(question, "section_id", None)

        # For questions with no ID (like new questions being created), we only validate their visibility conditions
        # but don't add them to validation errors
        is_new_question = not question_id

        # Validate repeat_section_id
        if repeat_section_id:
            repeat_section_id_str = str(repeat_section_id)

            # Check if the referenced section exists
            if repeat_section_id_str not in section_ids:
                errors.append(f"Question '{question_label}' references non-existent section ID '{repeat_section_id_str}'")
            else:
                # Check if the referenced section is marked as repeatable
                if repeat_section_id_str not in repeatable_sections:
                    errors.append(f"Question '{question_label}' references section '{repeat_section_id_str}' which is not marked as repeatable")

                # Check if the question type is appropriate for a repeatable controller
                if question_type not in ["number", "integer"]:
                    errors.append(f"Question '{question_label}' controls a repeatable section but has type '{question_type}' instead of 'number' or 'integer'")

                # Check if max_repeats is set
                if max_repeats is None:
                    errors.append(f"Question '{question_label}' controls a repeatable section but has no max_repeats value")
                elif not isinstance(max_repeats, int) or max_repeats <= 0:
                    errors.append(f"Question '{question_label}' has invalid max_repeats value: {max_repeats}")

                # Track this controller for the section (only if it has an ID)
                if not is_new_question:
                    repeatable_controllers[repeat_section_id_str] = question_id

        # Skip questions with no visibility condition
        if not visibility_condition:
            continue

        # Extract conditions from visibility condition
        if isinstance(visibility_condition, dict):
            operator = visibility_condition.get("operator", "AND")
            conditions = visibility_condition.get("conditions", [])
        else:
            operator = getattr(visibility_condition, "operator", "AND")
            conditions = getattr(visibility_condition, "conditions", [])

        # Validate operator
        if operator not in ["AND", "OR", "and", "or"]:  # Allow both uppercase and lowercase
            errors.append(f"Question '{question_label}' has invalid visibility condition operator: {operator}")

        # Validate each condition
        for condition in conditions:
            # Extract condition properties
            if isinstance(condition, dict):
                dependency_id = str(condition.get("question_id", ""))
                comparison = condition.get("comparison", "==")
                value = condition.get("value")
            else:
                dependency_id = str(getattr(condition, "question_id", ""))
                comparison = getattr(condition, "comparison", "==")
                value = getattr(condition, "value", None)

            # Check if the referenced question exists
            if dependency_id and dependency_id not in question_ids:
                # Check if it might be a scoped ID (for repeatable sections)
                if "_" in dependency_id:
                    base_id = dependency_id.split("_")[0]
                    if base_id not in question_ids:
                        errors.append(f"Question '{question_label}' references non-existent question ID '{dependency_id}'")
                else:
                    errors.append(f"Question '{question_label}' references non-existent question ID '{dependency_id}'")

            # Validate comparison operator
            valid_comparisons = ["==", "!=", ">", "<", ">=", "<=", "contains", "not_contains", "in", "not_in", "eq", "ne", "gt", "lt", "gte", "lte"]
            if comparison not in valid_comparisons:
                errors.append(f"Question '{question_label}' has invalid comparison operator: {comparison}")

            # Validate that the value is appropriate for the comparison
            if comparison in ["in", "not_in"] and not isinstance(value, list):
                errors.append(f"Question '{question_label}' uses {comparison} comparison but value is not a list: {value}")

    # Check that all repeatable sections have at least one controller
    # Only check this for existing questions, not for new ones being created
    
    # update wref 25/05/2025, 
    # no need for a question to hold the repeat section, 
    # rather show the whole section and the startups can add the number of times the sections should be repeated
    
    # for section_id in repeatable_sections:
    #     if section_id not in repeatable_controllers:
    #         section_title = section_map[section_id].get("title", section_id) if isinstance(section_map[section_id], dict) else getattr(section_map[section_id], "title", section_id)
    #         errors.append(f"Repeatable section '{section_title}' has no controller question")

    return errors


def validate_repeatable_section_nesting(
    questions: List[Union[Question, Dict[str, Any]]],
    section_map: Dict[str, Any]
) -> List[str]:
    """
    Validate that there is no nested repeatable section (only one level of nesting).

    Args:
        questions: List of Question objects or dictionaries
        section_map: Dictionary mapping section IDs to section objects

    Returns:
        A list of error messages, empty if no errors
    """
    errors = []

    for question in questions:
        # Handle both Question objects and dictionaries
        if isinstance(question, dict):
            question_label = question.get("label", "Unknown")
            repeat_section_id = question.get("repeat_section_id")
        else:
            question_label = question.label
            repeat_section_id = question.repeat_section_id

        if not repeat_section_id:
            continue

        repeat_section_id_str = str(repeat_section_id)
        if repeat_section_id_str in section_map:
            section = section_map[repeat_section_id_str]

            # Check if the section is already repeatable
            if section.get("repeatable", False):
                errors.append(f"Question '{question_label}' references section '{repeat_section_id_str}' which is already repeatable. Nested repeatable sections are not allowed.")

            # Check if any questions in the section reference another repeatable section
            section_questions = section.get("questions", [])
            for sq in section_questions:
                sq_repeat_section_id = sq.get("repeat_section_id") if isinstance(sq, dict) else getattr(sq, "repeat_section_id", None)
                if sq_repeat_section_id:
                    errors.append(f"Question '{question_label}' creates a nested repeatable section through section '{repeat_section_id_str}', which is not allowed.")

    return errors


def get_scoped_answer_key(question_id: str, section_instance_index: Optional[int] = None) -> str:
    """
    Generate a scoped answer key for a question in a repeatable section.

    Args:
        question_id: The ID of the question
        section_instance_index: The instance index of the repeatable section

    Returns:
        A scoped answer key in the format "question_id__instance_index"
    """
    if section_instance_index is not None:
        return f"{question_id}__{section_instance_index}"
    return question_id


def get_repeat_count(answers: Dict[str, Any], question_id: str) -> int:
    """
    Get the repeat count from a numeric answer.

    Args:
        answers: Dictionary of question_id -> answer or structured format with answers/repeatable_answers
        question_id: The ID of the question that determines the repeat count

    Returns:
        The number of repeats (0 if not found or invalid)
    """
    try:
        # Check if we have a structured format with 'answers' key
        if isinstance(answers, dict) and 'answers' in answers:
            count = int(answers['answers'].get(question_id, 0))
        else:
            # Regular flat dictionary
            count = int(answers.get(question_id, 0))

        return max(0, min(count, 100))  # Reasonable limits
    except (ValueError, TypeError):
        return 0
