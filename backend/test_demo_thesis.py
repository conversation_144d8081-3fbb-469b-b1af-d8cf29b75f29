#!/usr/bin/env python3
"""
Test script for demo thesis creation.

This script tests the create_demo_thesis method in the ThesisService.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.services.form.mongo import FormService
from app.services.thesis.mongo import ThesisService
from app.utils.common import PyObjectId
from motor.motor_asyncio import AsyncIOMotorClient

# Test organization ID
test_org_id = PyObjectId("507f1f77bcf86cd799439011")


async def test_create_demo_thesis():
    """Test creating a demo thesis."""
    print("🧪 Testing create_demo_thesis method...")

    # Connect to MongoDB
    client = AsyncIOMotorClient("mongodb://localhost:27017")
    db = client.tractionx_test

    # Initialize services
    form_service = FormService(db)
    thesis_service = ThesisService(db)

    # Initialize the services
    await form_service.initialize()
    await thesis_service.initialize()

    try:
        # Create a default form first
        print("Creating default form...")
        default_form = await form_service.create_default_form(test_org_id)
        if not default_form:
            print("❌ Failed to create default form")
            return False

        print("✅ Default form created successfully!")
        print(f"Form ID: {default_form['_id']}")

        # Create demo thesis
        print("Creating demo thesis...")
        demo_thesis = await thesis_service.create_demo_thesis(
            str(test_org_id), str(default_form["_id"])
        )

        if not demo_thesis:
            print("❌ Failed to create demo thesis")
            return False

        print("✅ Demo thesis created successfully!")
        print(f"Thesis ID: {demo_thesis.id}")
        print(f"Thesis Name: {demo_thesis.name}")
        print(f"Match Rules: {len(demo_thesis.match_rules)}")
        print(f"Scoring Rules: {len(demo_thesis.scoring_rules)}")

        return True

    except Exception as e:
        print(f"❌ Error testing create_demo_thesis: {e}")
        import traceback

        traceback.print_exc()
        return False
    finally:
        client.close()


if __name__ == "__main__":
    print("🧪 Testing create_demo_thesis method...")
    success = asyncio.run(test_create_demo_thesis())
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n💥 Test failed!")
        sys.exit(1)
