#!/usr/bin/env python3
"""
Test script for deal invite functionality.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_database
from app.services.factory import get_deal_service, get_queue_service
from app.models.organization import Organization
from app.models.form import Form
from bson import ObjectId


async def test_deal_invite():
    """Test the deal invite functionality."""
    try:
        print("🧪 Testing Deal Invite Functionality")
        print("=" * 50)
        
        # Initialize database
        db = await get_database()
        print("✅ Database connected")
        
        # Get services
        deal_service = await get_deal_service()
        await deal_service.initialize()
        print("✅ Deal service initialized")
        
        queue_service = await get_queue_service()
        await queue_service.initialize()
        print("✅ Queue service initialized")
        
        # Find a test organization
        org = await Organization.find_one(query={})
        if not org:
            print("❌ No organization found in database")
            return
        print(f"✅ Found organization: {org.name}")
        
        # Find a test form
        form = await Form.find_one(query={"is_active": True})
        if not form:
            print("❌ No active form found in database")
            return
        print(f"✅ Found form: {form.name}")
        
        # Test deal creation with invite
        print("\n🚀 Creating deal with invite...")
        deal = await deal_service.create_deal_with_invite(
            org_id=org.id,
            form_id=form.id,
            created_by=ObjectId(),  # Mock user ID
            company_name="Test Startup Inc",
            invited_email="<EMAIL>",
            company_website="teststartup.com",
            notes="Test deal for invite functionality",
            stage="Seed",
            sector="AI"
        )
        
        if deal:
            print(f"✅ Deal created successfully: {deal.id}")
            print(f"   Company: {deal.company_name}")
            print(f"   Email: {deal.invited_email}")
            print(f"   Status: {deal.invite_status}")
            print(f"   Timeline events: {len(deal.timeline)}")
        else:
            print("❌ Failed to create deal")
            return
        
        # Check if email job was queued
        print("\n📧 Checking queue for email job...")
        jobs = await queue_service.list_jobs(limit=10)
        email_jobs = [job for job in jobs if job.get("type") == "send_deal_invite_email"]

        if email_jobs:
            print(f"✅ Found {len(email_jobs)} email job(s) in queue")
            for job in email_jobs:
                print(f"   Job ID: {job.get('id')}")
                print(f"   Status: {job.get('status')}")
                print(f"   Type: {job.get('type')}")
        else:
            print("⚠️  No email jobs found in queue")
            print(f"   Found {len(jobs)} total jobs")
            for job in jobs[:3]:  # Show first 3 jobs
                print(f"   Job: {job.get('type', 'unknown')} - {job.get('status', 'unknown')}")
        
        print("\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_deal_invite())
