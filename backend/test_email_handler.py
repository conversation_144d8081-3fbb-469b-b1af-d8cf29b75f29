#!/usr/bin/env python3
"""
Test script for email handler functionality.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.queue.handlers.deal_invite import send_deal_invite_email
from app.models.organization import Organization
from app.models.form import Form


async def test_email_handler():
    """Test the email handler functionality."""
    try:
        print("📧 Testing Email Handler Functionality")
        print("=" * 50)
        
        # Find a test organization
        org = await Organization.find_one(query={})
        if not org:
            print("❌ No organization found in database")
            return
        print(f"✅ Found organization: {org.name}")
        
        # Find a test form
        form = await Form.find_one(query={"is_active": True})
        if not form:
            print("❌ No active form found in database")
            return
        print(f"✅ Found form: {form.name}")
        
        # Test email handler
        print("\n📨 Testing email handler...")
        payload = {
            "deal_id": "684a81a60a56a4882e70a39c",  # Use the deal ID from previous test
            "org_id": str(org.id),
            "form_id": str(form.id),
            "company_name": "Test Startup Inc",
            "invited_email": "<EMAIL>",
            "company_website": "teststartup.com",
            "pitch_deck_url": None
        }
        
        result = await send_deal_invite_email(payload)
        
        if result.get("success"):
            print("✅ Email handler executed successfully")
            print(f"   Deal ID: {result.get('deal_id')}")
            print(f"   Email: {result.get('invited_email')}")
            print(f"   Form Link: {result.get('form_link')}")
        else:
            print("❌ Email handler failed")
            print(f"   Error: {result.get('error')}")
        
        print("\n🎉 Email handler test completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_email_handler())
