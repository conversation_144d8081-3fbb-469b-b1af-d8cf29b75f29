#!/usr/bin/env python3
"""
Test script for the create_default_form method.
This script tests the creation of a comprehensive demo form.
"""

import asyncio
import os
import sys

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.services.form.mongo import FormService
from app.utils.common import PyObjectId
from motor.motor_asyncio import AsyncIOMotorClient


async def test_create_default_form():
    """Test the create_default_form method."""
    try:
        # Connect to MongoDB (adjust connection string as needed)
        client = AsyncIOMotorClient("mongodb://localhost:27017")
        db = client.tractionx_test  # Use test database

        # Create form service
        form_service = FormService(db)

        # Test organization ID
        test_org_id = PyObjectId("507f1f77bcf86cd799439011")

        print("Creating default form...")
        result = await form_service.create_default_form(test_org_id)

        if result:
            print("✅ Default form created successfully!")
            print(f"Form ID: {result.get('_id')}")
            print(f"Form Name: {result.get('name')}")
            print(f"Form Description: {result.get('description')}")
            print(f"Number of sections: {len(result.get('sections', []))}")
            print(f"Default sections: {len(result.get('default_section_ids', []))}")

            # Get form details to verify sections and questions
            form_details = await form_service.get_form_with_details(
                str(result.get("_id"))
            )
            if form_details:
                print("\n📋 Form Details:")
                print(f"Total sections: {len(form_details.sections)}")

                for i, section in enumerate(form_details.sections):
                    print(f"\nSection {i + 1}: {section.title}")
                    print(f"  Description: {section.description}")
                    print(f"  Questions: {len(section.questions)}")
                    print(f"  Repeatable: {section.repeatable}")

                    for j, question in enumerate(section.questions):
                        print(f"    Q{j + 1}: {question.label} ({question.type})")
                        if question.visibility_condition:
                            print("      ⚡ Has visibility condition")
                        if question.core_field:
                            print(f"      🎯 Core field: {question.core_field}")
                        if question.validation:
                            print("      ✅ Has validation rules")

            return True
        else:
            print("❌ Failed to create default form")
            return False

    except Exception as e:
        print(f"❌ Error testing create_default_form: {str(e)}")
        import traceback

        traceback.print_exc()
        return False
    finally:
        if "client" in locals():
            client.close()


if __name__ == "__main__":
    print("🧪 Testing create_default_form method...")
    success = asyncio.run(test_create_default_form())
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n💥 Test failed!")
        sys.exit(1)
