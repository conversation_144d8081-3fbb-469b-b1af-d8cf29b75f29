/**
 * Deal Invite API Client
 * 
 * Handles deal creation with invite functionality (PRD 1)
 */

import apiClient from "../api-client";



export interface CreateDealWithInviteRequest {
  company_name: string;
  company_website?: string;
  invited_email: string;
  form_id: string;
  stage?: string;
  sector?: string;
  pitch_deck_file?: string; // S3 key after upload
  notes?: string;
}

export interface DealInviteResponse {
  id: string;
  org_id: string;
  form_id: string;
  company_name: string;
  company_website?: string;
  invited_email: string;
  invite_status: string;
  invite_sent_at?: number;
  stage?: string;
  sector?: string[];
  pitch_deck_url?: string;
  notes?: string;
  created_at: number;
  updated_at: number;
}

export interface FormOption {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
}

export interface FileUploadResponse {
  presigned_url: string;
  public_url: string;
  s3_key: string;
  expires_in: number;
}

export class DealInviteAPI {
  /**
   * Get available forms for deal invites
   */
  static async getAvailableForms(): Promise<FormOption[]> {
    try {
      const response = await apiClient.get('/forms');
      
      // The backend returns forms directly as an array, not wrapped in a 'forms' property
      const forms = response.data || [];
      
      // Ensure forms is an array before mapping
      if (!Array.isArray(forms)) {
        console.warn('Forms response is not an array:', forms);
        return [];
      }
      
      // Filter for active forms on the frontend since backend doesn't support filtering
      const activeForms = forms.filter((form: any) => form.is_active !== false);
      
      return activeForms.map((form: any) => ({
        id: form.id || form._id, // Handle both id and _id fields
        name: form.name,
        description: form.description,
        is_active: form.is_active
      }));
    } catch (error) {
      console.error('Error fetching available forms:', error);
      // Return empty array instead of throwing to prevent breaking the deals page
      return [];
    }
  }

  /**
   * Upload pitch deck file and get S3 key
   */
  static async uploadPitchDeck(file: File): Promise<string> {
    try {
      // Step 1: Get presigned URL using the correct endpoint
      const uploadResponse = await apiClient.post('/uploads/generate-presigned-url', {
        filename: file.name,
        content_type: file.type,
        file_size: file.size,
        file_type: 'pitch_deck'
      });

      const { presigned_url, s3_key } = uploadResponse.data;

      // Step 2: Upload file to S3
      await fetch(presigned_url, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      return s3_key;
    } catch (error) {
      console.error('Error uploading pitch deck:', error);
      throw error;
    }
  }

  /**
   * Create deal with invite functionality
   */
  static async createDealWithInvite(
    dealData: CreateDealWithInviteRequest
  ): Promise<DealInviteResponse> {
    try {
      const response = await apiClient.post('/deals', {
        org_id: undefined, // Will be set by backend from context
        form_id: dealData.form_id,
        submission_id: undefined, // No submission for invite-first deals
        company_name: dealData.company_name,
        company_website: dealData.company_website,
        invited_email: dealData.invited_email,
        stage: dealData.stage,
        sector: dealData.sector,
        pitch_deck_file: dealData.pitch_deck_file,
        notes: dealData.notes,
        status: 'new'
      });

      return response.data;
    } catch (error) {
      console.error('Error creating deal with invite:', error);
      throw error;
    }
  }

  /**
   * Get deal details including invite status
   */
  static async getDealDetails(dealId: string): Promise<DealInviteResponse> {
    try {
      const response = await apiClient.get(`/deals/${dealId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching deal details:', error);
      throw error;
    }
  }

  /**
   * Update invite status (used by tracking endpoints)
   */
  static async updateInviteStatus(
    dealId: string, 
    status: string,
    submissionId?: string
  ): Promise<DealInviteResponse> {
    try {
      const response = await apiClient.patch(`/deals/${dealId}/invite-status`, {
        status,
        submission_id: submissionId
      });
      return response.data;
    } catch (error) {
      console.error('Error updating invite status:', error);
      throw error;
    }
  }

  /**
   * Get deals with invite tracking information
   */
  static async getDealsWithInvites(params?: {
    skip?: number;
    limit?: number;
    invite_status?: string;
    search?: string;
  }): Promise<{
    deals: DealInviteResponse[];
    total: number;
    skip: number;
    limit: number;
    has_more: boolean;
  }> {
    try {
      const response = await apiClient.get('/deals', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching deals with invites:', error);
      throw error;
    }
  }

  /**
   * Resend invite email for a deal
   */
  static async resendInvite(dealId: string): Promise<{ success: boolean }> {
    try {
      const response = await apiClient.post(`/deals/${dealId}/resend-invite`);
      return response.data;
    } catch (error) {
      console.error('Error resending invite:', error);
      throw error;
    }
  }

  /**
   * Get context block for a deal (PRD 2)
   */
  static async getContextBlock(dealId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/deals/${dealId}/context`);
      return response.data;
    } catch (error) {
      console.error('Error fetching context block:', error);
      throw error;
    }
  }
}
