// API client for public submission flow

const getApiBase = () => {
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  }
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
};

const API_BASE = getApiBase();

export interface PublicLoginRequest {
  email: string;
  name?: string;
  token: string;
}

export interface PublicLoginResponse {
  message: string;
  email: string;
  requires_verification: boolean;
}

export interface MagicLinkVerifyResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: string;
    email: string;
    name?: string;
    type: 'public';
  };
  submission?: {
    id: string;
    status: string;
    progress: number;
    can_edit: boolean;
    answers?: Record<string, any>;
    last_question?: string;
  };
  can_edit: boolean;
  redirect_url?: string;
}

export interface PublicSubmission {
  id: string;
  status: 'draft' | 'submitted';
  progress: number;
  can_edit: boolean;
  answers: Record<string, any>;
  last_question?: string;
}

export interface SubmissionProgressRequest {
  answers: Record<string, any>;
  progress: number;
  last_question?: string;
}

export interface SubmissionProgressResponse {
  id: string;
  status: string;
  progress: number;
  message: string;
}

export interface SubmitResponse {
  id: string;
  status: string;
  message: string;
  public_submission_id: string;
}

export interface TokenRefreshResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export class PublicSubmissionAPI {
  /**
   * Login with email and sharing token
   */
  static async login(request: PublicLoginRequest): Promise<PublicLoginResponse> {
    try {
      const response = await fetch(`${API_BASE}/public/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to send magic link');
      }

      return response.json();
    } catch (error) {
      console.error('Public login error:', error);
      throw error;
    }
  }

  /**
   * Verify magic link token
   */
  static async verifyMagicLink(token: string): Promise<MagicLinkVerifyResponse> {
    try {
      const response = await fetch(`${API_BASE}/public/magic-link/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({token}),
      });


      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Invalid or expired magic link');
      }

      return response.json();
    } catch (error) {
      console.error('Magic link verification error:', error);
      throw error;
    }
  }

  /**
   * Get submission by token and email
   */
  static async getSubmission(token: string, email: string): Promise<PublicSubmission> {
    try {
      const response = await fetch(
        `${API_BASE}/public/submission/${token}?email=${encodeURIComponent(email)}`
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to get submission');
      }

      return response.json();
    } catch (error) {
      console.error('Get submission error:', error);
      throw error;
    }
  }

  /**
   * Save submission progress with automatic token refresh
   */
  static async saveProgress(
    submissionId: string,
    request: SubmissionProgressRequest,
    accessToken?: string
  ): Promise<SubmissionProgressResponse> {
    const attemptSave = async (token?: string): Promise<SubmissionProgressResponse> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE}/public/submission/${submissionId}/progress`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        // Check if it's a token expiration error
        if (response.status === 401 && errorData.detail?.includes('expired')) {
          const error = new Error(errorData.detail || 'Token has expired');
          (error as any).isTokenExpired = true;
          throw error;
        }
        
        throw new Error(errorData.detail || 'Failed to save progress');
      }

      return response.json();
    };

    try {
      // First attempt with current token
      return await attemptSave(accessToken);
    } catch (error: any) {
      // If token expired, try to refresh and retry
      if (error.isTokenExpired && accessToken) {
        try {
          // Get refresh token from localStorage
          const refreshToken = typeof window !== 'undefined' 
            ? localStorage.getItem('public_refresh_token') 
            : null;
            
          if (refreshToken) {
            console.log('Attempting token refresh...');
            const refreshResponse = await this.refreshToken(refreshToken);
            
            // Update tokens in localStorage
            if (typeof window !== 'undefined') {
              localStorage.setItem('public_access_token', refreshResponse.access_token);
              localStorage.setItem('public_refresh_token', refreshResponse.refresh_token);
            }
            
            // Retry the original request with new token
            console.log('Retrying request with new token...');
            return await attemptSave(refreshResponse.access_token);
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // If refresh fails, throw the original error
          throw error;
        }
      }
      
      // If not a token error or refresh failed, throw original error
      throw error;
    }
  }

  /**
   * Submit final submission with automatic token refresh
   */
  static async submitSubmission(
    submissionId: string,
    accessToken?: string
  ): Promise<SubmitResponse> {
    const attemptSubmit = async (token?: string): Promise<SubmitResponse> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE}/public/submission/${submissionId}/submit`, {
        method: 'POST',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        // Check if it's a token expiration error
        if (response.status === 401 && errorData.detail?.includes('expired')) {
          const error = new Error(errorData.detail || 'Token has expired');
          (error as any).isTokenExpired = true;
          throw error;
        }
        
        throw new Error(errorData.detail || 'Failed to submit');
      }

      return response.json();
    };

    try {
      // First attempt with current token
      return await attemptSubmit(accessToken);
    } catch (error: any) {
      // If token expired, try to refresh and retry
      if (error.isTokenExpired && accessToken) {
        try {
          // Get refresh token from localStorage
          const refreshToken = typeof window !== 'undefined' 
            ? localStorage.getItem('public_refresh_token') 
            : null;
            
          if (refreshToken) {
            console.log('Attempting token refresh for submission...');
            const refreshResponse = await this.refreshToken(refreshToken);
            
            // Update tokens in localStorage
            if (typeof window !== 'undefined') {
              localStorage.setItem('public_access_token', refreshResponse.access_token);
              localStorage.setItem('public_refresh_token', refreshResponse.refresh_token);
            }
            
            // Retry the original request with new token
            console.log('Retrying submission with new token...');
            return await attemptSubmit(refreshResponse.access_token);
          }
        } catch (refreshError) {
          console.error('Token refresh failed during submission:', refreshError);
          // If refresh fails, throw the original error
          throw error;
        }
      }
      
      // If not a token error or refresh failed, throw original error
      throw error;
    }
  }

  /**
   * Get sharing token details (form info, organization, etc.)
   */
  static async getTokenDetails(token: string): Promise<{
    form: any;
    organization: any;
    sharing_config: any;
    branding?: any;
  }> {
    try {
      const response = await fetch(`${API_BASE}/forms/share/${token}/details`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('This form link has expired or is invalid.');
        }
        if (response.status >= 500) {
          throw new Error('Server error. Please try again later.');
        }
        throw new Error('Failed to load form details.');
      }

      return response.json();
    } catch (error) {
      console.error('Get token details error:', error);
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(refreshToken: string): Promise<TokenRefreshResponse> {
    try {
      const response = await fetch(`${API_BASE}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({refresh_token: refreshToken}),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to refresh token');
      }

      return response.json();
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }
}
