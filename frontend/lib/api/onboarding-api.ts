import axios from 'axios';
import { env } from '@/env.mjs';

// Define the API base URL
const API_BASE_URL = env.NEXT_PUBLIC_API_URL;

// Interfaces for request and response types
interface CreateInviteCodeRequest {
  email: string;
  org_name?: string;
}

interface CreateInviteCodeResponse {
  code: string;
  email: string;
  expires_at: number;
  onboard_url: string;
}

interface ValidateInviteCodeResponse {
  valid: boolean;
  email: string;
  org_name?: string;
  expires_at: number;
}

interface OnboardingStepOneRequest {
  org_name: string;
  subdomain: string;
  website?: string;
}

interface OnboardingStepTwoRequest {
  name: string;
  password: string;
  confirm_password: string;
}

interface OnboardingCompleteResponse {
  user_id: string;
  org_id: string;
  access_token: string;
  refresh_token: string;
  redirect_url: string;
}

interface SubdomainCheckResponse {
  subdomain: string;
  available: boolean;
}

interface PeerInviteRequest {
  emails: string[];
  role_id?: string;
  message?: string;
}

interface PeerInviteResponse {
  total_sent: number;
  total_failed: number;
  results: Array<{
    email: string;
    status: 'sent' | 'failed';
    result?: any;
    error?: string;
  }>;
}

// Create the onboarding API client
const OnboardingAPI = {
  /**
   * Create an invite code for organization onboarding
   */
  async createInviteCode(request: CreateInviteCodeRequest): Promise<CreateInviteCodeResponse> {
    try {
      console.log('Creating invite code for:', request.email);

      const response = await axios.post(`${API_BASE_URL}/auth/public/create-invite-code`, request, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Invite code created successfully');
      return response.data;
    } catch (error: any) {
      console.error('Create invite code error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to create invite code');
    }
  },

  /**
   * Validate an invite code
   */
  async validateInviteCode(code: string): Promise<ValidateInviteCodeResponse> {
    try {
      console.log('Validating invite code:', code);

      const response = await axios.get(`${API_BASE_URL}/onboard/${code}/validate`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Invite code validation successful');
      return response.data;
    } catch (error: any) {
      console.error('Validate invite code error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to validate invite code');
    }
  },

  /**
   * Check subdomain availability
   */
  async checkSubdomainAvailability(subdomain: string): Promise<SubdomainCheckResponse> {
    try {
      console.log('Checking subdomain availability:', subdomain);

      const response = await axios.get(`${API_BASE_URL}/onboard/check-subdomain/${subdomain}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Subdomain check successful');
      return response.data;
    } catch (error: any) {
      console.error('Subdomain check error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to check subdomain availability');
    }
  },

  /**
   * Onboarding Step 1: Create organization
   */
  async onboardingStepOne(code: string, request: OnboardingStepOneRequest): Promise<{ success: boolean; org_id: string; message: string }> {
    try {
      console.log('Onboarding step 1 for code:', code);

      const response = await axios.post(`${API_BASE_URL}/onboard/${code}/step-1`, request, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Onboarding step 1 successful');
      return response.data;
    } catch (error: any) {
      console.error('Onboarding step 1 error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to complete onboarding step 1');
    }
  },

  /**
   * Onboarding Step 2: Create user profile and complete onboarding
   */
  async onboardingStepTwo(code: string, request: OnboardingStepTwoRequest): Promise<OnboardingCompleteResponse> {
    try {
      console.log('Onboarding step 2 for code:', code);

      const response = await axios.post(`${API_BASE_URL}/onboard/${code}/step-2`, request, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Onboarding step 2 successful');
      
      // Don't store tokens - user should log in manually after onboarding
      return response.data;
    } catch (error: any) {
      console.error('Onboarding step 2 error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to complete onboarding step 2');
    }
  },

  /**
   * Send peer invitations
   */
  async sendPeerInvites(request: PeerInviteRequest): Promise<PeerInviteResponse> {
    try {
      console.log('Sending peer invites to:', request.emails);

      // Get auth headers
      const token = localStorage.getItem('token');
      const orgId = localStorage.getItem('orgId');

      if (!token || !orgId) {
        throw new Error('Authentication required');
      }

      const response = await axios.post(`${API_BASE_URL}/auth/public/invite-peers`, request, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'X-ORG-ID': orgId,
        },
      });

      console.log('Peer invites sent successfully');
      return response.data;
    } catch (error: any) {
      console.error('Send peer invites error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to send peer invites');
    }
  },
};

export default OnboardingAPI;
export type {
  CreateInviteCodeRequest,
  CreateInviteCodeResponse,
  ValidateInviteCodeResponse,
  OnboardingStepOneRequest,
  OnboardingStepTwoRequest,
  OnboardingCompleteResponse,
  SubdomainCheckResponse,
  PeerInviteRequest,
  PeerInviteResponse,
};
