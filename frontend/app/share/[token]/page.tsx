"use client"

import { useParams } from 'next/navigation';
import { SharedFormRenderer } from '@/components/core/form-share/shared-form-renderer';
import { ShareErrorBoundary } from '@/components/core/form-share/share-error-boundary';
import { PublicAuthProvider } from '@/lib/contexts/public-auth-context';

export default function ShareFormPage() {
  const params = useParams();
  const token = params?.token as string;

  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Invalid Link</h1>
          <p className="text-gray-600">This sharing link is invalid or malformed.</p>
        </div>
      </div>
    );
  }

  return (
    <PublicAuthProvider>
      <ShareErrorBoundary>
        <SharedFormRenderer token={token} />
      </ShareErrorBoundary>
    </PublicAuthProvider>
  );
}
