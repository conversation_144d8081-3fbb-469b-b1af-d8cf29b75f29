"use client"

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { FormBuilder } from '@/components/core/form-builder';

export default function EditFormPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  const formId = params?.id as string;

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return null;
  }

  // Render the new Form Builder with the form ID
  // The FormBuilder will handle loading, error states, and all form operations
  return <FormBuilder formId={formId} />;
}
