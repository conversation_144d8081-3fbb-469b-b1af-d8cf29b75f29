"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { toast } from "sonner"

import { AuthLayout } from "@/components/auth/auth-layout"
import { OnboardingWizard } from "@/components/core/onboarding/onboarding-wizard"
import OnboardingAPI, { ValidateInviteCodeResponse } from "@/lib/api/onboarding-api"

export default function OnboardPage() {
  const params = useParams()
  const router = useRouter()
  const code = params?.code as string

  const [inviteData, setInviteData] = useState<ValidateInviteCodeResponse | null>(null)
  const [isValidating, setIsValidating] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!code) {
      setError("Invalid invite code")
      setIsValidating(false)
      return
    }

    validateInviteCode()
  }, [code])

  const validateInviteCode = async () => {
    try {
      setIsValidating(true)
      setError(null)

      const response = await OnboardingAPI.validateInviteCode(code)
      
      if (response.valid) {
        setInviteData(response)
      } else {
        setError("Invalid or expired invite code")
      }
    } catch (err: any) {
      console.error("Invite code validation error:", err)
      setError(err.message || "Failed to validate invite code")
    } finally {
      setIsValidating(false)
    }
  }

  const handleOnboardingComplete = (result: any) => {
    console.log("Onboarding completed:", result)
    
    // Show success message
    toast.success("Welcome to TractionX! 🎉", {
      description: "Your organization has been set up successfully. Please log in to continue.",
    })

    // Redirect to login page
    setTimeout(() => {
      router.push("/login")
    }, 1500)
  }

  if (isValidating) {
    return (
      <AuthLayout
        title="Validating Invite"
        subtitle="Please wait while we validate your invite code..."
      >
        <div className="flex items-center justify-center py-12">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="h-8 w-8 border-2 border-primary border-t-transparent rounded-full"
          />
        </div>
      </AuthLayout>
    )
  }

  if (error || !inviteData) {
    return (
      <AuthLayout
        title="Invalid Invite"
        subtitle="This invite code is not valid or has expired"
      >
        <div className="text-center py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="text-6xl">😔</div>
            <p className="text-muted-foreground">{error}</p>
            <button
              onClick={() => router.push("/login")}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              Go to Login
            </button>
          </motion.div>
        </div>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout
      title="Welcome to TractionX"
      subtitle="Let's set up your organization and get you started"
    >
      <OnboardingWizard
        inviteCode={code}
        inviteData={inviteData}
        onComplete={handleOnboardingComplete}
      />
    </AuthLayout>
  )
}
