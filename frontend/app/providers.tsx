"use client"

import React from 'react'
import { AuthProvider } from '@/lib/auth-context'
import { PublicAuthProvider } from '@/lib/contexts/public-auth-context'
import { ThemeProvider } from '@/components/theme-provider'

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      forcedTheme="light"
      enableSystem={false}
      disableTransitionOnChange
    >
      <AuthProvider>
        <PublicAuthProvider>
          {children}
        </PublicAuthProvider>
      </AuthProvider>
    </ThemeProvider>
  )
}
