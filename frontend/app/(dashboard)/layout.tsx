"use client"

import { dashboardConfig } from "@/config/dashboard"
import { MainNav } from "@/components/main-nav"
import { SiteFooter } from "@/components/site-footer"
import { UserAccountNav } from "@/components/user-account-nav"
import { ProtectedRoute } from "@/components/protected-route"
import { OrganizationSelector } from "@/components/org-selector"
import { EnhancedSidebar } from "@/components/enhanced-sidebar"
import { useAuth } from "@/lib/auth-context"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const [mounted, setMounted] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Handle hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return <DashboardLayoutContent 
    sidebarOpen={sidebarOpen}
    setSidebarOpen={setSidebarOpen}
    mobileMenuOpen={mobileMenuOpen}
    setMobileMenuOpen={setMobileMenuOpen}
  >
    {children}
  </DashboardLayoutContent>;
}

function DashboardLayoutContent({
  children,
  sidebarOpen,
  setSidebarOpen,
  mobileMenuOpen,
  setMobileMenuOpen,
}: {
  children: React.ReactNode;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean | ((prev: boolean) => boolean)) => void;
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
}) {
  const { user } = useAuth();

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen flex-col">
        {/* Mobile-first header */}
        <header className={cn(
          "sticky top-0 z-30 border-b bg-background/95 backdrop-blur-sm",
          "transition-all duration-300 safe-top",
          // Desktop sidebar spacing
          sidebarOpen ? 'md:ml-[280px]' : 'md:ml-[80px]'
        )}>
          <div className="flex h-16 items-center justify-between px-4 md:px-6 lg:px-8">
            <div className="flex items-center gap-3">
              {/* Mobile Menu Button - Enhanced */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className={cn(
                  "md:hidden rounded-xl p-2 transition-all duration-200",
                  "hover:bg-accent active:scale-95 touch-target",
                  "focus:outline-none focus:ring-2 focus:ring-ring"
                )}
                aria-label="Toggle mobile menu"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={mobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                  />
                </svg>
              </button>
              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center gap-4">
                <MainNav items={dashboardConfig.mainNav} />
                <OrganizationSelector />
              </div>

              {/* Mobile: Show org selector only */}
              <div className="md:hidden">
                <OrganizationSelector />
              </div>
            </div>

            {/* User Account Navigation */}
            {user && (
              <UserAccountNav
                user={{
                  name: user.name,
                  image: null,
                  email: user.email,
                }}
              />
            )}
          </div>
        </header>
        {/* Enhanced Sidebar - Fixed Positioning */}
        <div className="hidden md:block">
          <EnhancedSidebar
            items={dashboardConfig.sidebarNav}
            isCollapsed={!sidebarOpen}
            onToggleCollapse={() => setSidebarOpen(!sidebarOpen)}
          />
        </div>

        {/* Mobile Sidebar Overlay */}
        {mobileMenuOpen && (
          <>
            <div 
              className="fixed inset-0 bg-black/50 z-30 md:hidden"
              onClick={() => setMobileMenuOpen(false)}
            />
            <div className="fixed left-0 top-0 h-full w-[280px] z-50 md:hidden">
              <EnhancedSidebar
                items={dashboardConfig.sidebarNav}
                isCollapsed={false}
                onToggleCollapse={() => setMobileMenuOpen(false)}
              />
            </div>
          </>
        )}

        {/* Main Content - Mobile-first responsive */}
        <main
          className={cn(
            "flex-1 min-h-screen overflow-x-hidden transition-all duration-300",
            // Mobile-first padding
            "px-4 py-6 md:px-6 lg:px-8",
            // Desktop sidebar spacing
            sidebarOpen ? 'md:ml-[280px]' : 'md:ml-[80px]',
            // Safe area for mobile
            "safe-bottom"
          )}
          style={{ minHeight: 'calc(100vh - 4rem)' }}
        >
          <div className="w-full max-w-none">
            {children}
          </div>
        </main>

        {/* Footer - Mobile-first responsive */}
        <SiteFooter
          className={cn(
            "border-t transition-all duration-300 safe-bottom",
            sidebarOpen ? 'md:ml-[280px]' : 'md:ml-[80px]'
          )}
        />
    </div>
    </ProtectedRoute>
  )
}
