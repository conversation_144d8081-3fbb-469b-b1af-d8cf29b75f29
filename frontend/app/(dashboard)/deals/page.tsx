"use client"

import { useState, useEffect, useMemo } from "react"
import { useRouter } from "next/navigation"
import { Shell } from "@/components/shell"
import { DealsGrid, DealsHeader } from "@/components/core/deals"
import { NewDealModal } from "@/components/core/deals/new-deal-modal"
import { DealAPI } from "@/lib/api/deal-api"
import { DealInviteAPI } from "@/lib/api/deal-invite-api"
import { Deal, DealStatus } from "@/lib/types/deal"
import { useAuth } from "@/lib/auth-context"
import { useToast } from "@/components/ui/use-toast"
// Mock deals removed - will use API data only

export default function DealsPage() {
  const router = useRouter()
  const { isAuthenticated, user, token, orgId, loading: authLoading } = useAuth()
  const { toast } = useToast()
  const [allDeals, setAllDeals] = useState<Deal[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeFilter, setActiveFilter] = useState('all')
  const [showNewDealModal, setShowNewDealModal] = useState(false)
  const [availableForms, setAvailableForms] = useState<any[]>([])

  // Debug authentication state
  useEffect(() => {
    console.log('DealsPage - Auth state:', {
      isAuthenticated,
      hasUser: !!user,
      hasToken: !!token,
      hasOrgId: !!orgId,
      authLoading,
      tokenPreview: token ? token.substring(0, 20) + '...' : 'none',
      orgId
    });
  }, [isAuthenticated, user, token, orgId, authLoading]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login?from=/deals');
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    const fetchData = async () => {
      if (!isAuthenticated) {
        console.log('User not authenticated, skipping data fetch');
        setLoading(false)
        setError('Please log in to view deals')
        return
      }

      if (!token || !orgId) {
        console.log('Missing token or orgId, skipping data fetch');
        setLoading(false)
        setError('Authentication incomplete. Please log in again.')
        return
      }

      try {
        setLoading(true)
        setError(null)

        console.log('Fetching deals and forms with auth:', {
          hasToken: !!token,
          orgId,
          tokenPreview: token.substring(0, 20) + '...'
        });

        // Fetch deals and forms in parallel
        const [dealsResponse, formsResponse] = await Promise.all([
          DealAPI.listDeals(0, 100).catch((error: any) => {
            console.error('Error fetching deals:', error);
            // If it's an auth error, don't show it as a general error
            if (error.response?.status === 401) {
              console.log('Authentication error when fetching deals');
              return { deals: [] };
            }
            throw error;
          }),
          DealInviteAPI.getAvailableForms().catch(() => []) // Don't fail if forms can't be loaded
        ])

        setAllDeals(dealsResponse.deals || [])
        setAvailableForms(formsResponse)
      } catch (err: any) {
        console.error('Error fetching data:', err)
        
        // Handle specific error types
        if (err.response?.status === 401) {
          setError('Authentication required. Please log in again.')
        } else if (err.response?.status === 500) {
          setError('Server error. Please try again later.')
        } else {
          setError('Failed to load deals. Please check your connection and try again.')
        }
        
        setAllDeals([])
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [isAuthenticated, token, orgId])

  // Filter and search deals
  const filteredDeals = useMemo(() => {
    let filtered = allDeals

    // Apply status filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(deal => deal.status === activeFilter)
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(deal =>
        deal.company_name?.toLowerCase().includes(query) ||
        (Array.isArray(deal.sector)
          ? deal.sector.some(s => s.toLowerCase().includes(query))
          : deal.sector?.toLowerCase().includes(query)) ||
        deal.stage?.toLowerCase().includes(query)
      )
    }

    return filtered
  }, [allDeals, activeFilter, searchQuery])

  const handleSearchChange = (search: string) => {
    setSearchQuery(search)
  }

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter)
  }

  // Calculate deal counts for filters
  const dealCounts = useMemo(() => ({
    all: allDeals.length,
    new: allDeals.filter(d => d.status === DealStatus.NEW).length,
    triage: allDeals.filter(d => d.status === DealStatus.TRIAGE).length,
    reviewed: allDeals.filter(d => d.status === DealStatus.REVIEWED).length,
    approved: allDeals.filter(d => d.status === DealStatus.APPROVED).length,
    excluded: allDeals.filter(d => d.status === DealStatus.EXCLUDED).length,
    closed: allDeals.filter(d => d.status === DealStatus.CLOSED).length,
  }), [allDeals])

  const handleNewDeal = () => {
    setShowNewDealModal(true)
  }

  const handleCreateDeal = async (dealData: any) => {
    try {
      let pitchDeckS3Key: string | undefined

      // Upload pitch deck if provided
      if (dealData.pitch_deck) {
        toast({
          title: 'Uploading',
          description: 'Uploading pitch deck...'
        })
        pitchDeckS3Key = await DealInviteAPI.uploadPitchDeck(dealData.pitch_deck)
      }

      // Create deal with invite
      const newDeal = await DealInviteAPI.createDealWithInvite({
        company_name: dealData.company_name,
        company_website: dealData.company_website,
        invited_email: dealData.contact_email,
        form_id: dealData.form_id,
        stage: dealData.stage,
        sector: dealData.sector,
        pitch_deck_file: pitchDeckS3Key,
        notes: dealData.notes,
      })

      // Add to local state
      const dealForState: Deal = {
        id: newDeal.id,
        org_id: newDeal.org_id,
        form_id: newDeal.form_id,
        submission_ids: [],
        company_name: newDeal.company_name,
        company_website: newDeal.company_website,
        stage: newDeal.stage,
        sector: newDeal.sector,
        status: DealStatus.NEW,
        notes: newDeal.notes,
        created_by: 'current-user',
        created_at: newDeal.created_at,
        updated_at: newDeal.updated_at
      }

      setAllDeals(prev => [dealForState, ...prev])

      toast({
        title: 'Success',
        description: `Deal "${dealData.company_name}" created and invite sent to ${dealData.contact_email}.`
      })

    } catch (error) {
      console.error('Error creating deal:', error)
      toast({
        title: 'Error',
        description: 'Failed to create deal. Please try again.',
        variant: 'destructive'
      })
      throw error
    }
  }

  return (
    <Shell className="max-w-none">
      <div className="space-y-8">
        {/* Show loading while checking authentication */}
        {authLoading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Checking authentication...</span>
          </div>
        ) : !isAuthenticated ? (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <p className="text-muted-foreground">Redirecting to login...</p>
            </div>
          </div>
        ) : (
          <>
            <DealsHeader
              onSearchChange={handleSearchChange}
              onFilterChange={handleFilterChange}
              activeFilter={activeFilter}
              totalDeals={allDeals.length}
              dealCounts={dealCounts}
            />

            <DealsGrid
              deals={filteredDeals}
              loading={loading}
              error={error}
              onNewDeal={handleNewDeal}
            />

            {/* New Deal Modal */}
            <NewDealModal
              open={showNewDealModal}
              onOpenChange={setShowNewDealModal}
              onSubmit={handleCreateDeal}
              availableForms={availableForms}
            />
          </>
        )}
      </div>
    </Shell>
  )
}
