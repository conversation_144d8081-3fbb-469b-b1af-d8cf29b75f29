"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { toast } from "sonner"

import { SettingsLayout } from "@/components/core/settings/settings-layout"
import { ProfileForm } from "@/components/core/settings/profile-form"
import { ChangePasswordModal } from "@/components/core/settings/change-password-modal"
import { SettingsAPI, UserProfile } from "@/lib/api/settings-api"
import { useAuth } from "@/lib/auth-context"

export default function ProfileSettingsPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showPasswordModal, setShowPasswordModal] = useState(false)

  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    try {
      setIsLoading(true)
      const profileData = await SettingsAPI.getProfile()
      setProfile(profileData)
    } catch (error: any) {
      console.error("Failed to load profile:", error)
      toast.error("Failed to load profile")
    } finally {
      setIsLoading(false)
    }
  }

  const handleProfileUpdate = async (data: { name?: string; profile_picture?: string }) => {
    try {
      await SettingsAPI.updateProfile(data)
      toast.success("Profile updated successfully!")
      
      // Reload profile to get updated data
      await loadProfile()
    } catch (error: any) {
      console.error("Failed to update profile:", error)
      toast.error(error.message || "Failed to update profile")
    }
  }

  const handlePasswordChange = async (data: {
    current_password: string
    new_password: string
    confirm_password: string
  }) => {
    try {
      await SettingsAPI.changePassword(data)
      toast.success("Password changed successfully!")
      setShowPasswordModal(false)
    } catch (error: any) {
      console.error("Failed to change password:", error)
      toast.error(error.message || "Failed to change password")
      throw error // Re-throw to keep modal open
    }
  }

  if (isLoading) {
    return (
      <SettingsLayout activeTab="profile">
        <div className="flex items-center justify-center py-12">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="h-8 w-8 border-2 border-primary border-t-transparent rounded-full"
          />
        </div>
      </SettingsLayout>
    )
  }

  if (!profile) {
    return (
      <SettingsLayout activeTab="profile">
        <div className="text-center py-12">
          <p className="text-muted-foreground">Failed to load profile</p>
        </div>
      </SettingsLayout>
    )
  }

  return (
    <SettingsLayout activeTab="profile">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <ProfileForm
          profile={profile}
          onUpdate={handleProfileUpdate}
          onChangePassword={() => setShowPasswordModal(true)}
        />
      </motion.div>

      <ChangePasswordModal
        open={showPasswordModal}
        onOpenChange={setShowPasswordModal}
        onSubmit={handlePasswordChange}
      />
    </SettingsLayout>
  )
}
