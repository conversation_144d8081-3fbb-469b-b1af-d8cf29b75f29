"use client"

import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  TrendingUp, 
  FileText, 
  Target, 
  ArrowRight,
  Plus
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { DashboardSummary } from '@/lib/api/dashboard-api';
import Link from 'next/link';

interface QuickActionsProps {
  dashboardData: DashboardSummary;
  className?: string;
}

interface ActionButton {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  primary?: boolean;
}

export function QuickActions({ dashboardData, className }: QuickActionsProps) {
  const actions: ActionButton[] = [
    {
      title: `View Deals (${dashboardData.active_deals})`,
      description: dashboardData.active_deals > 0 ? 'Manage your deal pipeline' : 'No deals yet - create a form to get started',
      href: '/deals',
      icon: <TrendingUp className="h-5 w-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      primary: true
    },
    {
      title: `View Forms (${dashboardData.forms})`,
      description: dashboardData.forms > 0 ? 'Create and manage forms' : 'Create your first form to start collecting applications',
      href: '/forms',
      icon: <FileText className="h-5 w-5" />,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50 hover:bg-emerald-100'
    },
    {
      title: `View Theses (${dashboardData.theses})`,
      description: dashboardData.theses > 0 ? 'Configure investment criteria' : 'Build your investment thesis to score deals',
      href: '/theses',
      icon: <Target className="h-5 w-5" />,
      color: 'text-violet-600',
      bgColor: 'bg-violet-50 hover:bg-violet-100'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.4
      }
    }
  };

  const buttonVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const hoverVariants = {
    hover: {
      scale: 1.02,
      transition: {
        duration: 0.2,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("space-y-4", className)}
    >
      {/* Section Header */}
      <motion.div variants={buttonVariants}>
        <h3 className="text-xl font-bold text-foreground mb-3">Quick Actions</h3>
        <p className="text-base text-muted-foreground">
          Jump to key areas of your investment workflow
        </p>
      </motion.div>

      {/* Action Buttons Grid - Mobile-first */}
      <motion.div
        variants={containerVariants}
        className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6"
      >
        {actions.map((action, index) => (
          <motion.div
            key={action.title}
            variants={buttonVariants}
            whileHover="hover"
          >
            <Link href={action.href}>
              <motion.div variants={hoverVariants}>
                <Card className={cn(
                  "group cursor-pointer transition-all duration-300 border-0 shadow-md",
                  "hover:shadow-lg active:scale-[0.98]",
                  "touch-manipulation" // Optimize for touch
                )}>
                  <CardContent className={cn(
                    // Mobile-first content padding
                    "p-4 md:p-6"
                  )}>
                    <div className={cn(
                      "flex items-start",
                      // Mobile: stack vertically, desktop: horizontal
                      "flex-col gap-3 xs:flex-row xs:gap-4"
                    )}>
                      <div className={cn(
                        "rounded-xl transition-colors duration-200",
                        // Mobile-first icon sizing
                        "p-2 md:p-3",
                        action.bgColor
                      )}>
                        <div className={action.color}>
                          {action.icon}
                        </div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors">
                            {action.title}
                          </h4>
                          <ArrowRight className="h-5 w-5 text-muted-foreground group-hover:text-foreground group-hover:translate-x-1 transition-all duration-200" />
                        </div>
                        <p className="text-base text-muted-foreground line-clamp-2">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Link>
          </motion.div>
        ))}
      </motion.div>

      {/* Create New Section */}
      <motion.div variants={buttonVariants} className="pt-6">
        <Card className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-950/20 dark:to-blue-950/20 border-slate-200 dark:border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-semibold text-lg text-foreground mb-2">Create Something New</h4>
                <p className="text-base text-muted-foreground">
                  Start building your investment workflow
                </p>
              </div>
              <div className="flex gap-3">
                <Link href="/forms">
                  <Button size="sm" variant="outline" className="gap-2">
                    <Plus className="h-4 w-4" />
                    New Form
                  </Button>
                </Link>
                <Link href="/theses">
                  <Button size="sm" className="gap-2">
                    <Plus className="h-4 w-4" />
                    New Thesis
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
