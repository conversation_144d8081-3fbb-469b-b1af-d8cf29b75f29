"use client"

import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { Calculator, ChevronDown, ChevronRight, Info, Edit, Trash2, Play, RotateCcw, CheckSquare, Square, Save, Loader2 } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  QuestionScoringConfig,
  AggregationType,
  getAggregationDisplay,
  ConditionOperator,
  getOperatorDisplay,
  ScoringRule,
  RuleType,
  FilterCondition
} from '@/lib/types/thesis';
import { QuestionType, Form } from '@/lib/types/form';
import { RuleEditor } from './rule-editor';
import { RuleSummary } from './rule-summary';
import { TestRuleModal } from './test-rule-modal';
import { toast } from '@/components/ui/use-toast';

interface ScoringConfigTableProps {
  questionConfigs: QuestionScoringConfig[];
  onQuestionConfigChange: (questionId: string, config: Partial<QuestionScoringConfig>) => void;
  onCreateScoringRule?: (questionId: string, rule: Partial<ScoringRule>) => Promise<void>;
  onUpdateScoringRule?: (ruleId: string, rule: Partial<ScoringRule>) => Promise<void>;
  onDeleteScoringRule?: (ruleId: string) => Promise<void>;
  existingScoringRules?: ScoringRule[];
  form?: Form;
  thesisId?: string; // Add thesis ID to check if thesis is saved
}

export function ScoringConfigTable({
  questionConfigs,
  onQuestionConfigChange,
  onCreateScoringRule,
  onUpdateScoringRule,
  onDeleteScoringRule,
  existingScoringRules = [],
  form,
  thesisId
}: ScoringConfigTableProps) {
  console.log('📊 ScoringConfigTable props:', { questionConfigs, existingScoringRules });
  console.log('🔍 Existing scoring rules detailed:', existingScoringRules.map(rule => ({
    questionId: rule.question_id,
    id: rule.id,
    _id: rule._id,
    ruleType: rule.rule_type,
    weight: rule.weight
  })));

  // State for UI interactions
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [editingRule, setEditingRule] = useState<{
    questionId: string;
    rule?: ScoringRule;
    questionType: QuestionType;
    questionLabel: string;
    isRepeatable: boolean;
    sectionId: string;
  } | null>(null);
  const [testingRule, setTestingRule] = useState<{
    rule: ScoringRule;
    questionType: QuestionType;
    questionLabel: string;
  } | null>(null);
  const [savingRules, setSavingRules] = useState<Set<string>>(new Set());
  const [isSaving, setIsSaving] = useState(false);
  const [isSavingAll, setIsSavingAll] = useState(false);

  // Group questions by section
  const questionsBySection = questionConfigs.reduce((acc, config) => {
    const sectionTitle = config.section_title || 'Uncategorized';
        if (!acc[sectionTitle]) {
          acc[sectionTitle] = [];
        }
        acc[sectionTitle].push(config);
        return acc;
      }, {} as Record<string, QuestionScoringConfig[]>);

  // Toggle section expansion
  const toggleSection = (sectionTitle: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle]
    }));
  };

  // Bulk operations
  const handleBulkInclude = (sectionTitle?: string) => {
    const configsToUpdate = sectionTitle
      ? questionsBySection[sectionTitle] || []
      : questionConfigs;

    configsToUpdate.forEach(config => {
      // Skip file questions - they cannot be included
      if (!config.included && config.question_type !== 'file') {
        onQuestionConfigChange(config.question_id, { included: true });
      }
    });
  };

  const handleBulkExclude = (sectionTitle?: string) => {
    const configsToUpdate = sectionTitle
      ? questionsBySection[sectionTitle] || []
      : questionConfigs;

    configsToUpdate.forEach(config => {
      // File questions are already excluded by default, but allow explicit exclusion
      if (config.included) {
        onQuestionConfigChange(config.question_id, { included: false });
      }
    });
  };

  const handleResetWeights = (sectionTitle?: string) => {
    const configsToUpdate = sectionTitle
      ? questionsBySection[sectionTitle] || []
      : questionConfigs;

    configsToUpdate.forEach(config => {
      if (config.included && config.weight !== 1) {
        onQuestionConfigChange(config.question_id, { weight: 1 });
      }
    });
  };

  // Helper to get question options from form data
  const getQuestionOptions = (questionId: string) => {
    if (!form?.sections) {
      return [];
    }

    for (const section of form.sections) {
      const question = section.questions?.find(q => {
        const qId = q._id || q.id;
        return qId === questionId;
      });

      if (question) {
        // For boolean questions, return standard true/false options
        if (question.type === 'boolean') {
          return [
            { label: 'True', value: 'true' },
            { label: 'False', value: 'false' }
          ];
        }
        // For MCQ questions, return the actual options
        return question.options || [];
      }
    }

    return [];
  };

  // Rule management with proper API calls
  const handleCreateRule = (questionId: string) => {
    const config = questionConfigs.find(c => c.question_id === questionId);
    if (!config) {
      return;
    }

    setEditingRule({
      questionId,
      questionType: config.question_type as QuestionType,
      questionLabel: config.question_label,
      isRepeatable: config.is_repeatable,
      sectionId: config.section_id // Use actual section_id instead of section_title
    });
  };

  const handleEditRule = (questionId: string, rule: ScoringRule) => {
    const config = questionConfigs.find(c => c.question_id === questionId);
    if (!config) return;

    setEditingRule({
      questionId,
      rule,
      questionType: config.question_type as QuestionType,
      questionLabel: config.question_label,
      isRepeatable: config.is_repeatable,
      sectionId: config.section_id // Use actual section_id instead of section_title
    });
  };

  const handleSaveRule = async (rule: Partial<ScoringRule>) => {
    console.log('🔥 SCORING CONFIG TABLE handleSaveRule called with:', rule);
    if (!editingRule) {
      console.log('❌ No editingRule state, aborting save');
      return;
    }

    const questionId = editingRule.questionId;
    console.log('🎯 Saving rule for questionId:', questionId);
    setSavingRules(prev => new Set(prev).add(questionId));

    try {
      // Validate required fields before API call
      if (!rule.rule_type) {
        throw new Error('Rule type is required');
      }

      if (rule.rule_type === RuleType.SCORING) {
        if (!rule.question_id && !questionId) {
          throw new Error('Question ID is required for scoring rules');
        }
        if (!rule.weight || rule.weight <= 0) {
          throw new Error('Weight must be a positive number');
        }
        if (!rule.condition) {
          throw new Error('Condition is required for scoring rules');
        }
      }

      if (rule.rule_type === RuleType.BONUS) {
        if (!rule.bonus_points || rule.bonus_points <= 0) {
          throw new Error('Bonus points must be a positive number');
        }
        if (!rule.condition) {
          throw new Error('Condition is required for bonus rules');
        }
      }

      // Ensure question_id and section_id are set for the rule
      const ruleData = {
        ...rule,
        question_id: rule.question_id || questionId,
        // Include section_id for aggregation support if available
        ...(editingRule.sectionId && { section_id: editingRule.sectionId })
      };

      if (editingRule.rule && (editingRule.rule.id || editingRule.rule._id)) {
        // Update existing rule
        const ruleId = editingRule.rule.id || editingRule.rule._id as string;
        console.log('🔄 Updating scoring rule:', ruleId, ruleData);
        
        if (!onUpdateScoringRule) {
          throw new Error('Update scoring rule function not provided');
        }
        
        console.log('🚀 Calling onUpdateScoringRule API function...');
        await onUpdateScoringRule(ruleId, ruleData);
        
        // Update local question config to sync weight
        if (ruleData.weight) {
          onQuestionConfigChange(questionId, { 
            weight: ruleData.weight,
            included: true 
          });
        }
        
        toast({
          title: 'Rule Updated',
          description: 'Scoring rule has been updated successfully'
        });
      } else {
        // Create new rule
        if (!onCreateScoringRule) {
          throw new Error('Create scoring rule function not provided');
        }

        console.log('🚀 Calling onCreateScoringRule API function...');
        await onCreateScoringRule(questionId, ruleData);
        
        // Update local question config to sync weight and include
        onQuestionConfigChange(questionId, { 
          weight: ruleData.weight || 1,
          included: true 
        });
        
        toast({
          title: 'Rule Created',
          description: 'Scoring rule has been created successfully'
        });
      }

      setEditingRule(null);
    } catch (error) {
      console.error('❌ Error saving rule:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save rule';
      toast({
        title: 'Error Saving Rule',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setSavingRules(prev => {
        const newSet = new Set(prev);
        newSet.delete(questionId);
        return newSet;
      });
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    console.log('🗑️ Attempting to delete scoring rule:', ruleId);
    
    // Early validation to provide better error feedback
    if (!ruleId || ruleId.trim() === '') {
      console.error('❌ Delete attempted with empty rule ID');
      toast({
        title: 'Cannot Delete Rule',
        description: 'Rule ID is missing. This may be a data consistency issue.',
        variant: 'destructive'
      });
      return;
    }
    
    try {
      if (!onDeleteScoringRule) {
        throw new Error('Delete scoring rule function not provided');
      }
      
      console.log('🚀 Calling delete function with rule ID:', ruleId);
      await onDeleteScoringRule(ruleId);
      
      toast({
        title: 'Rule Deleted',
        description: 'Scoring rule has been deleted successfully'
      });
    } catch (error) {
      console.error('❌ Error deleting rule:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete rule';
      toast({
        title: 'Error Deleting Rule',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const handleTestRule = (questionId: string, rule?: ScoringRule) => {
    const config = questionConfigs.find(c => c.question_id === questionId);
    if (!config) return;

    const testRule = rule || {
      rule_type: RuleType.SCORING,
      weight: config.weight || 1,
      condition: {
        question_id: questionId,
        operator: ConditionOperator.EQUALS,
        value: config.expected_value || ''
      },
      thesis_id: '', // Required for ScoringRule type
      is_deleted: false // Required for ScoringRule type
    } as ScoringRule;

    setTestingRule({
      rule: testRule,
      questionType: config.question_type as QuestionType,
      questionLabel: config.question_label
    });
  };

  // Get existing rule for a question (prevent duplicates)
  const getExistingRule = (questionId: string): ScoringRule | undefined => {
    return existingScoringRules.find(rule => rule.question_id === questionId);
  };

  // Check if a rule already exists for a question
  const hasExistingRule = (questionId: string): boolean => {
    return !!getExistingRule(questionId);
  };

  // Handle weight changes with immediate sync
  const handleWeightChange = (questionId: string, weight: number) => {
    onQuestionConfigChange(questionId, { weight });
    
    // If there's an existing rule, update it immediately
    const existingRule = getExistingRule(questionId);
    if (existingRule && onUpdateScoringRule) {
      const ruleId = existingRule.id || existingRule._id as string;
      onUpdateScoringRule(ruleId, { weight }).catch(error => {
        console.error('Failed to sync weight to rule:', error);
        toast({
          title: 'Warning',
          description: 'Weight updated locally but failed to sync to rule. Please save the rule manually.',
          variant: 'destructive'
        });
      });
    }
  };

  // Handle include/exclude toggle with proper state management
  const handleIncludeToggle = (questionId: string, included: boolean) => {
    // Find the question config to check if it's a file question
    const config = questionConfigs.find(c => c.question_id === questionId);
    
    // Block file questions from being included
    if (config?.question_type === 'file' && included) {
      toast({
        title: "File Questions Not Scorable",
        description: "File upload questions cannot be used for scoring rules.",
        variant: "destructive",
      });
      return;
    }
    
    onQuestionConfigChange(questionId, { included });
    
    if (!included) {
      // When excluding, we might want to delete the rule or mark it as excluded
      const existingRule = getExistingRule(questionId);
      if (existingRule) {
        // For now, we'll keep the rule but user can delete it manually
        // In the future, we might want to add an "exclude_from_scoring" flag
        console.log('Question excluded but rule still exists:', existingRule);
      }
    }
  };

  const includedCount = questionConfigs.filter(config => config.included).length;
  const totalWeight = questionConfigs
    .filter(config => config.included)
    .reduce((sum, config) => sum + (config.weight || 0), 0);

  // Get all questions that need saving (either modified configs or missing rules)
  const getUnsavedQuestions = useCallback(() => {
    const unsavedQuestions: string[] = [];

    // FIX: questionConfigs is an array, not an object - iterate properly
    questionConfigs.forEach((config) => {
      // Skip file questions - they cannot have scoring rules
      if (config.included && config.question_type !== 'file') {
        const existingRule = existingScoringRules.find(rule =>
          rule.question_id === config.question_id && !rule.is_deleted
        );

        // Question needs saving if:
        // 1. It's included but has no rule, OR
        // 2. It's included and the weight differs from the existing rule
        if (!existingRule || (existingRule.weight !== config.weight)) {
          unsavedQuestions.push(config.question_id);
        }
      }
    });

    return unsavedQuestions;
  }, [questionConfigs, existingScoringRules]);

  const unsavedQuestions = useMemo(() => getUnsavedQuestions(), [getUnsavedQuestions]);
  const hasUnsavedChanges = unsavedQuestions.length > 0;

  // Save all scoring configurations
  const handleSaveAllScoringRules = useCallback(async () => {
    if (!hasUnsavedChanges) {
      toast({
        title: "No changes to save",
        description: "All scoring configurations are up to date.",
      });
      return;
    }

    setIsSavingAll(true);
    let savedCount = 0;
    let errorCount = 0;

    try {
      for (const questionId of unsavedQuestions) {
        try {
          // FIX: Find config by question_id since questionConfigs is an array
          const config = questionConfigs.find(c => c.question_id === questionId);
          if (!config?.included || config.question_type === 'file') continue;

          const existingRule = existingScoringRules.find(rule =>
            rule.question_id === questionId && !rule.is_deleted
          );

          // Create basic scoring rule data - handle text questions differently
          const isTextQuestion = config.question_type === 'short_text' || config.question_type === 'long_text';

          const ruleData = {
            rule_type: RuleType.SCORING,
            question_id: questionId,
            weight: config.weight || 1,
            condition: {
              question_id: questionId,
              operator: ConditionOperator.EQUALS,
              value: isTextQuestion ? {
                good_reference: 'Please configure good reference examples',
                bad_reference: 'Please configure bad reference examples'
              } : true
            } as FilterCondition
          };

          if (existingRule) {
            // Update existing rule
            if (onUpdateScoringRule) {
              await onUpdateScoringRule(existingRule.id || existingRule._id!, ruleData);
            }
          } else {
            // Create new rule
            if (onCreateScoringRule) {
              await onCreateScoringRule(questionId, ruleData);
            }
          }

          savedCount++;
        } catch (error) {
          console.error(`Error saving scoring rule for question ${questionId}:`, error);
          errorCount++;
        }
      }

      if (savedCount > 0) {
        toast({
          title: "Scoring rules saved",
          description: `Successfully saved ${savedCount} scoring rule(s).${errorCount > 0 ? ` ${errorCount} failed to save.` : ''}`,
          variant: errorCount > 0 ? "destructive" : "default",
        });
      }
    } catch (error) {
      console.error('Error in save all operation:', error);
      toast({
        title: "Error saving scoring rules",
        description: "Failed to save scoring rules. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSavingAll(false);
    }
  }, [hasUnsavedChanges, unsavedQuestions, questionConfigs, existingScoringRules, onUpdateScoringRule, onCreateScoringRule]);

  if (questionConfigs.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Scoring Configuration
          </CardTitle>
          <CardDescription>
            Configure how questions will be scored for this thesis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground bg-muted/50 rounded-lg">
            <Calculator className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="font-medium">Select a form first</p>
            <p className="text-sm">Choose a form to configure scoring for its questions.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
    <Card>
      <CardHeader>
          <div className="flex items-center justify-between">
            <div>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Scoring Configuration
        </CardTitle>
        <CardDescription>
                Configure how questions will be scored for this thesis
        </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              {hasUnsavedChanges && (
                <Button
                  onClick={handleSaveAllScoringRules}
                  disabled={isSavingAll}
                  className="flex items-center gap-2"
                  type="button"
                >
                  {isSavingAll ? (
                    <>
                      <Loader2 className="size-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="size-4" />
                      Save All Changes
                    </>
                  )}
                </Button>
              )}
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Included:</span>
                  <Badge variant="outline">{includedCount}</Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Total Weight:</span>
                  <Badge variant="outline">{totalWeight.toFixed(1)}</Badge>
                </div>
              </div>
            </div>
          </div>
      </CardHeader>
        <CardContent>
          {/* Thesis Save Warning */}
          {!thesisId && (
            <Alert className="mb-4">
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Save thesis first:</strong> You must save the thesis (name, description, and form) before creating scoring rules. 
                Use the "Save Thesis" button above to enable rule creation.
              </AlertDescription>
            </Alert>
          )}

          {/* Global Actions */}
          <div className="flex items-center gap-2 mb-4 p-3 bg-muted/50 rounded-lg">
            <span className="text-sm font-medium">Bulk Actions:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkInclude()}
              className="h-8"
            >
              <CheckSquare className="h-3 w-3 mr-1" />
              Include All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkExclude()}
              className="h-8"
            >
              <Square className="h-3 w-3 mr-1" />
              Exclude All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleResetWeights()}
              className="h-8"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset Weights
            </Button>
          </div>

          {/* Questions by Section */}
        <div className="space-y-4">
          {Object.entries(questionsBySection).map(([sectionTitle, configs]) => {
            const sectionIncludedCount = configs.filter(config => config.included).length;
              const sectionTotalWeight = configs
                .filter(config => config.included)
                .reduce((sum, config) => sum + (config.weight || 0), 0);

            return (
              <Collapsible
                key={sectionTitle}
                  open={expandedSections[sectionTitle] ?? true}
                onOpenChange={() => toggleSection(sectionTitle)}
              >
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-2">
                      {expandedSections[sectionTitle] ?? true ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                      <span className="font-medium">{sectionTitle}</span>
                      <Badge variant="outline" className="text-xs">
                        {configs.length} questions
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {sectionIncludedCount} included
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {sectionTotalWeight.toFixed(1)} weight
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1">
                      <div
                        className="inline-flex items-center justify-center h-7 px-2 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleBulkInclude(sectionTitle);
                        }}
                        title="Include all questions in this section"
                      >
                        <CheckSquare className="h-3 w-3" />
                      </div>
                      <div
                        className="inline-flex items-center justify-center h-7 px-2 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleBulkExclude(sectionTitle);
                        }}
                        title="Exclude all questions in this section"
                      >
                        <Square className="h-3 w-3" />
                      </div>
                      <div
                        className="inline-flex items-center justify-center h-7 px-2 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleResetWeights(sectionTitle);
                        }}
                        title="Reset all weights to 1.0"
                      >
                        <RotateCcw className="h-3 w-3" />
                      </div>
                    </div>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-12">Include</TableHead>
                          <TableHead>Question</TableHead>
                          <TableHead className="w-16">Weight</TableHead>
                            <TableHead>Rule Summary</TableHead>
                            <TableHead className="w-32">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                          {configs.map((config) => {
                            const existingRule = getExistingRule(config.question_id);
                            const isSaving = savingRules.has(config.question_id);

                            return (
                          <TableRow key={config.question_id}>
                            <TableCell>
                              {config.question_type === 'file' ? (
                                <div className="flex items-center gap-2">
                                  <Switch 
                                    checked={false} 
                                    disabled={true}
                                    className="opacity-50"
                                  />
                                  <span className="text-xs text-muted-foreground">Not scorable</span>
                                </div>
                              ) : (
                                <Switch
                                  checked={config.included}
                                  onCheckedChange={(checked) => handleIncludeToggle(config.question_id, checked)}
                                />
                              )}
                            </TableCell>

                            <TableCell>
                              <div className="space-y-1">
                                <div className="font-medium">{config.question_label}</div>
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant={config.question_type === 'file' ? 'secondary' : 'outline'} 
                                    className={`text-xs ${config.question_type === 'file' ? 'bg-muted text-muted-foreground' : ''}`}
                                  >
                                    {config.question_type.replace('_', ' ').charAt(0).toUpperCase() + config.question_type.replace('_', ' ').slice(1)}
                                  </Badge>
                                  {config.question_type === 'file' && (
                                    <Badge variant="outline" className="text-xs border-red-500 text-red-600">
                                      Not Scorable
                                    </Badge>
                                  )}
                                  {config.is_repeatable && (
                                    <Badge variant="secondary" className="text-xs">
                                      Repeatable
                                    </Badge>
                                  )}
                                  {existingRule && (
                                    <Badge variant="default" className="text-xs">
                                      Has Rule
                                    </Badge>
                                  )}
                                  {!existingRule && config.included && (
                                    <Badge variant="outline" className="text-xs border-green-500 text-green-600">
                                      Needs Rule
                                    </Badge>
                                  )}
                                  {existingRule && config.weight !== existingRule.weight && (
                                    <Badge variant="outline" className="text-xs border-amber-500 text-amber-600">
                                      Weight Changed
                                    </Badge>
                                  )}
                                  {existingRule && existingRule.condition && 'value' in existingRule.condition &&
                                   typeof existingRule.condition.value === 'object' &&
                                   (existingRule.condition.value?.good_reference || existingRule.condition.value?.bad_reference) && (
                                    <Badge variant="outline" className="text-xs border-purple-500 text-purple-600">
                                      AI Text Scoring
                                    </Badge>
                                  )}
                                  {isSaving && (
                                    <Badge variant="outline" className="text-xs border-blue-500 text-blue-600">
                                      Saving...
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </TableCell>

                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                step="0.1"
                                value={config.question_type === 'file' ? '' : (config.weight || '')}
                                onChange={(e) => handleWeightChange(config.question_id, 
                                  e.target.value ? Number(e.target.value) : 1
                                )}
                                disabled={config.question_type === 'file' || !config.included || isSaving}
                                className="w-16"
                                placeholder={config.question_type === 'file' ? 'N/A' : ''}
                              />
                            </TableCell>

                            <TableCell>
                                  {existingRule ? (
                                    <div className="text-xs text-muted-foreground">
                                      <RuleSummary rule={existingRule} questionLabel={config.question_label} />
                                    </div>
                                  ) : (
                                    <div className="space-y-1">
                                      <span className="text-xs text-muted-foreground italic">No rule configured</span>
                                      {(config.question_type === 'single_select' || 
                                        config.question_type === 'multi_select' || 
                                        config.question_type === 'boolean') && (
                                        <div className="flex flex-wrap gap-1">
                                          {getQuestionOptions(config.question_id).slice(0, 3).map((option, idx) => (
                                            <Badge key={idx} variant="outline" className="text-xs h-5">
                                              {option.label}
                                            </Badge>
                                          ))}
                                          {getQuestionOptions(config.question_id).length > 3 && (
                                            <Badge variant="outline" className="text-xs h-5">
                                              +{getQuestionOptions(config.question_id).length - 3} more
                                            </Badge>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  )}
                            </TableCell>

                            <TableCell>
                                  <div className="flex items-center gap-1">
                                    {existingRule ? (
                                      <>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleEditRule(config.question_id, existingRule)}
                                          className="h-8 w-8 p-0"
                                          disabled={isSaving}
                                        >
                                          <Edit className="h-3 w-3" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleTestRule(config.question_id, existingRule)}
                                          className="h-8 w-8 p-0"
                                          disabled={isSaving}
                                        >
                                          <Play className="h-3 w-3" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => {
                                            console.log('🔍 Delete button clicked for rule:', existingRule);
                                            console.log('🔍 Rule ID fields:', { id: existingRule.id, _id: existingRule._id });
                                            const ruleId = existingRule.id || existingRule._id || '';
                                            console.log('🔍 Resolved rule ID:', ruleId);
                                            handleDeleteRule(ruleId);
                                          }}
                                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                          disabled={isSaving}
                                        >
                                          <Trash2 className="h-3 w-3" />
                                        </Button>
                                      </>
                                    ) : (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleCreateRule(config.question_id)}
                                        className="h-8 w-8 p-0"
                                        disabled={config.question_type === 'file' || !config.included || isSaving || !thesisId}
                                        title={
                                          config.question_type === 'file' 
                                            ? "File questions cannot be scored"
                                            : !thesisId 
                                              ? "Save thesis first to create rules" 
                                              : "Create scoring rule"
                                        }
                                      >
                                        <Edit className="h-3 w-3" />
                                      </Button>
                                    )}
                                  </div>
                            </TableCell>
                          </TableRow>
                            );
                          })}
                      </TableBody>
                    </Table>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            );
          })}
        </div>
      </CardContent>
    </Card>

      {/* Scoring Rules Help */}
      <Card className="border-dashed">
        <CardContent className="pt-6">
          <div className="space-y-3">
            <h4 className="font-medium text-sm">💡 Scoring Rules Tips</h4>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>Include questions that are relevant to your investment thesis</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>Adjust weights to reflect question importance (higher weight = more impact)</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>For MCQ and Boolean questions, you can select multiple expected values for flexible matching</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>For text questions, define examples of good and bad answers to guide LLM scoring</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>File upload questions cannot be scored and are automatically excluded</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>Use the "Save All Changes" button to sync all configurations at once</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rule Editor Modal */}
      {editingRule && (
        <RuleEditor
          isOpen={!!editingRule}
          onClose={() => setEditingRule(null)}
          onSave={handleSaveRule}
          rule={editingRule.rule}
          questionId={editingRule.questionId}
          questionType={editingRule.questionType}
          questionLabel={editingRule.questionLabel}
          isRepeatable={editingRule.isRepeatable}
          sectionId={editingRule.sectionId}
          allowBonusRules={false} // Only allow scoring rules in this context
          questionOptions={getQuestionOptions(editingRule.questionId)}
        />
      )}

      {/* Test Rule Modal */}
      {testingRule && (
        <TestRuleModal
          isOpen={!!testingRule}
          onClose={() => setTestingRule(null)}
          rule={testingRule.rule}
          questionType={testingRule.questionType}
          questionLabel={testingRule.questionLabel}
        />
      )}
    </>
  );
}
