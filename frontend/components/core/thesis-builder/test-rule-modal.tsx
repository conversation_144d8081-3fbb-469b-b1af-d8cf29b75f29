"use client"

import React, { useState } from 'react';
import { Play, CheckCircle, XCircle, AlertCircle, Zap } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  ScoringRule,
  RuleType,
  FilterCondition,
  ConditionOperator
} from '@/lib/types/thesis';
import { QuestionType } from '@/lib/types/form';
import { RuleSummary } from './rule-summary';

interface TestRuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  rule: Partial<ScoringRule>;
  questionType: QuestionType;
  questionLabel?: string;
}

interface TestResult {
  matches: boolean;
  score: number;
  explanation: string;
}

// Simple rule evaluation logic for testing
function evaluateRule(rule: Partial<ScoringRule>, testValue: any): TestResult {
  if (!rule.condition || !('operator' in rule.condition)) {
    return {
      matches: false,
      score: 0,
      explanation: 'Invalid rule condition'
    };
  }

  const condition = rule.condition as FilterCondition;
  const { operator, value: expectedValue } = condition;

  let matches = false;
  let explanation = '';

  try {
    switch (operator) {
      case ConditionOperator.EQUALS:
        matches = testValue === expectedValue;
        explanation = `${testValue} ${matches ? 'equals' : 'does not equal'} ${expectedValue}`;
        break;

      case ConditionOperator.NOT_EQUALS:
        matches = testValue !== expectedValue;
        explanation = `${testValue} ${matches ? 'does not equal' : 'equals'} ${expectedValue}`;
        break;

      case ConditionOperator.GREATER_THAN:
        matches = Number(testValue) > Number(expectedValue);
        explanation = `${testValue} ${matches ? 'is greater than' : 'is not greater than'} ${expectedValue}`;
        break;

      case ConditionOperator.LESS_THAN:
        matches = Number(testValue) < Number(expectedValue);
        explanation = `${testValue} ${matches ? 'is less than' : 'is not less than'} ${expectedValue}`;
        break;

      case ConditionOperator.GREATER_THAN_EQUALS:
        matches = Number(testValue) >= Number(expectedValue);
        explanation = `${testValue} ${matches ? 'is greater than or equal to' : 'is less than'} ${expectedValue}`;
        break;

      case ConditionOperator.LESS_THAN_EQUALS:
        matches = Number(testValue) <= Number(expectedValue);
        explanation = `${testValue} ${matches ? 'is less than or equal to' : 'is greater than'} ${expectedValue}`;
        break;

      case ConditionOperator.CONTAINS:
        matches = String(testValue).toLowerCase().includes(String(expectedValue).toLowerCase());
        explanation = `"${testValue}" ${matches ? 'contains' : 'does not contain'} "${expectedValue}"`;
        break;

      case ConditionOperator.NOT_CONTAINS:
        matches = !String(testValue).toLowerCase().includes(String(expectedValue).toLowerCase());
        explanation = `"${testValue}" ${matches ? 'does not contain' : 'contains'} "${expectedValue}"`;
        break;

      case ConditionOperator.STARTS_WITH:
        matches = String(testValue).toLowerCase().startsWith(String(expectedValue).toLowerCase());
        explanation = `"${testValue}" ${matches ? 'starts with' : 'does not start with'} "${expectedValue}"`;
        break;

      case ConditionOperator.ENDS_WITH:
        matches = String(testValue).toLowerCase().endsWith(String(expectedValue).toLowerCase());
        explanation = `"${testValue}" ${matches ? 'ends with' : 'does not end with'} "${expectedValue}"`;
        break;

      case ConditionOperator.IN:
        const inValues = Array.isArray(expectedValue) ? expectedValue : String(expectedValue).split(',').map(v => v.trim());
        matches = inValues.includes(testValue);
        explanation = `"${testValue}" ${matches ? 'is in' : 'is not in'} [${inValues.join(', ')}]`;
        break;

      case ConditionOperator.NOT_IN:
        const notInValues = Array.isArray(expectedValue) ? expectedValue : String(expectedValue).split(',').map(v => v.trim());
        matches = !notInValues.includes(testValue);
        explanation = `"${testValue}" ${matches ? 'is not in' : 'is in'} [${notInValues.join(', ')}]`;
        break;

      default:
        matches = false;
        explanation = `Unsupported operator: ${operator}`;
    }
  } catch (error) {
    matches = false;
    explanation = `Error evaluating condition: ${error}`;
  }

  const score = matches ? (rule.rule_type === RuleType.BONUS ? (rule.bonus_points || 0) : (rule.weight || 1)) : 0;

  return { matches, score, explanation };
}

function getInputType(questionType: QuestionType): string {
  switch (questionType) {
    case QuestionType.NUMBER:
    case QuestionType.RANGE:
      return 'number';
    case QuestionType.DATE:
      return 'date';
    case QuestionType.BOOLEAN:
      return 'checkbox';
    default:
      return 'text';
  }
}

function getPlaceholder(questionType: QuestionType): string {
  switch (questionType) {
    case QuestionType.SHORT_TEXT:
      return 'Enter text value...';
    case QuestionType.LONG_TEXT:
      return 'Enter longer text...';
    case QuestionType.NUMBER:
      return 'Enter number...';
    case QuestionType.RANGE:
      return 'Enter range value...';
    case QuestionType.BOOLEAN:
      return 'true/false';
    case QuestionType.SINGLE_SELECT:
      return 'Enter selected option...';
    case QuestionType.MULTI_SELECT:
      return 'Enter comma-separated options...';
    case QuestionType.DATE:
      return 'Select date...';
    default:
      return 'Enter test value...';
  }
}

export function TestRuleModal({
  isOpen,
  onClose,
  rule,
  questionType,
  questionLabel
}: TestRuleModalProps) {
  const [testValue, setTestValue] = useState<string>('');
  const [testResult, setTestResult] = useState<TestResult | null>(null);

  const handleTest = () => {
    let processedValue: any = testValue;

    // Process value based on question type
    if (questionType === QuestionType.NUMBER || questionType === QuestionType.RANGE) {
      processedValue = testValue ? Number(testValue) : 0;
    } else if (questionType === QuestionType.BOOLEAN) {
      processedValue = testValue.toLowerCase() === 'true' || testValue === '1';
    } else if (questionType === QuestionType.MULTI_SELECT) {
      processedValue = testValue.split(',').map(v => v.trim()).filter(v => v);
    }

    const result = evaluateRule(rule, processedValue);
    setTestResult(result);
  };

  const handleClose = () => {
    setTestValue('');
    setTestResult(null);
    onClose();
  };

  const inputType = getInputType(questionType);
  const placeholder = getPlaceholder(questionType);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Test Scoring Rule
          </DialogTitle>
          <DialogDescription>
            Test how this rule would evaluate with sample data.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Rule Summary */}
          <div className="space-y-2">
            <Label>Rule Being Tested</Label>
            <div className="p-3 bg-muted rounded-md text-sm">
              <RuleSummary rule={rule} questionLabel={questionLabel} />
            </div>
          </div>

          {/* Test Input */}
          <div className="space-y-2">
            <Label>Test Value</Label>
            {questionType === QuestionType.LONG_TEXT ? (
              <Textarea
                value={testValue}
                onChange={(e) => setTestValue(e.target.value)}
                placeholder={placeholder}
                className="min-h-[80px]"
              />
            ) : questionType === QuestionType.BOOLEAN ? (
              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="booleanValue"
                    value="true"
                    checked={testValue === 'true'}
                    onChange={(e) => setTestValue(e.target.value)}
                  />
                  <span>True</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="booleanValue"
                    value="false"
                    checked={testValue === 'false'}
                    onChange={(e) => setTestValue(e.target.value)}
                  />
                  <span>False</span>
                </label>
              </div>
            ) : (
              <Input
                type={inputType}
                value={testValue}
                onChange={(e) => setTestValue(e.target.value)}
                placeholder={placeholder}
              />
            )}
          </div>

          {/* Test Result */}
          {testResult && (
            <div className="space-y-2">
              <Label>Test Result</Label>
              <Alert variant={testResult.matches ? "default" : "destructive"}>
                {testResult.matches ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant={testResult.matches ? "default" : "secondary"}>
                        {testResult.matches ? 'Match' : 'No Match'}
                      </Badge>
                      <span className="font-medium">
                        Score: {testResult.score} point{testResult.score !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <p className="text-sm">{testResult.explanation}</p>
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
          <Button onClick={handleTest} disabled={!testValue.trim()}>
            <Play className="h-4 w-4 mr-2" />
            Test Rule
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
