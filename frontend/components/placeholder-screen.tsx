"use client"
import Image from "next/image"
import Link from "next/link"
import { motion } from 'framer-motion'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils";

interface PlaceholderScreenProps {
  title: string
  description: string
  imagePath: string
  buttonText?: string
  buttonLink?: string
}


import dynamic from "next/dynamic"
const PremiumWave = dynamic(() => import("@/components/ui/premium-wave"), { ssr: false })

// Keeping ParticleOrbitField for future use
// const ParticleOrbitField = dynamic(() => import("@/components/ui/particle-fields"), {
//   ssr: false,
// })

// PremiumWave component available for future use
// const PremiumWave = dynamic(() => import("@/components/ui/premium-wave"), {
//   ssr: false,
// })

const ClientOrbitPulseGrid = dynamic(() => import("@/components/core/orbit-ai/orbit-icon").then(mod => ({ default: mod.OrbitPulseGrid })), {
  ssr: false,
})

export default function OrbitHero() {
  return (
    <section className="relative min-h-screen px-6 sm:px-12 py-8 overflow-hidden">
      {/* White background layer */}
      <div className="absolute inset-0 bg-white z-0" />

      {/* Premium Wave - visually prominent, behind all content */}
      <div className="absolute inset-x-0 top-0 w-full h-[220px] z-10 pointer-events-none">
        <PremiumWave />
      </div>

      {/* Main Content (z-20 and above) */}
      <div className="relative z-20">
        {/* TractionX Logo - Top Left */}
        <div className="absolute top-8 left-8 z-30">
          <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
            <Image
              src="/images/traction.svg"
              alt="TractionX"
              width={150}
              height={150}
              className="drop-shadow-sm"
            />
          </Link>
        </div>

        {/* Login Button - Top Right */}
        {/* <div className="absolute top-8 right-8 z-30">
          <Button 
            asChild
            variant="outline"
            className="border-neutral-200 hover:border-neutral-300 bg-white/80 backdrop-blur-sm"
          >
            <Link href="/login">Login</Link>
          </Button>
        </div> */}

        {/* Main Content */}
        <div className="flex flex-col items-center justify-center min-h-[calc(110vh)]">
          {/* Main Headline with Premium Typography */}
          <h1 
            className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-center leading-tight tracking-tight text-neutral-900 mb-8"
            style={{ fontFamily: "'Playfair Display', 'Inter', serif" }}
          >
            Access to Private Markets,<br />
            <span className="bg-gradient-to-r from-neutral-900 via-neutral-700 to-neutral-900 bg-clip-text text-transparent">
              the only place you need to be!
            </span>
          </h1>

          {/* Orbit & Status Section */}
          <div 
            className="mt-12 flex flex-col md:flex-row items-center justify-center gap-3"
          >
            {/* OrbitPulseGrid with Breathing Effect - Client-only */}
            <div className="relative">
              <div className="relative z-10">
                <ClientOrbitPulseGrid
                  size={100}
                  dotCount={20}
                  duration={1500}
                  className="drop-shadow-lg"
                />
              </div>
              {/* Glow effect - positioned correctly */}
              <div className="absolute inset-0 rounded-full bg-neutral-900/5 blur-3xl z-0" />
            </div>

            {/* Status Text */}
            <div
              className="text-center md:text-left max-w-md"
            >
              <p className="text-lg tracking-widest text-neutral-700 font-semibold mb-1 font-mono">
                Orbit is live.
              </p>
              <p className="text-neutral-600 text-lg leading-relaxed font-light">
                The edge investors need to navigate excel in private market lifecycle
              </p>
            </div>
          </div>

          {/* Premium CTA Button */}
          <div 
            className="mt-16 relative"
          >
            <Link href="/login">
              <motion.button
                className={cn(
                  "group relative rounded-full px-8 py-4 sm:px-12 sm:py-5",
                  "bg-gradient-to-r from-neutral-900 via-neutral-800 to-neutral-900",
                  "border border-amber-400/30 hover:border-amber-400/60",
                  "font-mono text-sm sm:text-base font-medium tracking-wide",
                  "text-amber-50",
                  "transition-all duration-500 ease-out",
                  "shadow-2xl shadow-neutral-900/25 hover:shadow-3xl hover:shadow-neutral-900/40",
                  "overflow-hidden",
                  "hover:scale-105 active:scale-95"
                )}
                whileHover={{ 
                  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(251, 191, 36, 0.1)"
                }}
                whileTap={{ scale: 0.95 }}
              >
                {/* Animated shimmer effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-400/20 to-transparent"
                  animate={{
                    x: ["-100%", "200%"]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3,
                    ease: "easeInOut"
                  }}
                />

                {/* Button content with glow effect */}
                <span className="relative z-10 flex items-center justify-center space-x-3">
                  <span className="tracking-wider relative">
                    {/* Glow effect for the main text */}
                    <span className="absolute inset-0 text-amber-400/30 blur-sm">
                      WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS
                    </span>
                    <span className="relative z-10">
                      WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS
                    </span>
                  </span>
                  <motion.span 
                    className="text-amber-400 text-lg"
                    animate={{
                      x: [0, 4, 0]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    →
                  </motion.span>
                </span>

                {/* Subtle inner glow */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-amber-400/5 via-transparent to-amber-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              </motion.button>
            </Link>
          </div>
        </div>
      </div>

      {/* Subtle background elements - Lower z-index */}
      <div className="absolute inset-0 z-0">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-neutral-50 via-white to-neutral-100" />

        {/* Subtle grid pattern */}
        <div 
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgb(0 0 0) 1px, transparent 0)`,
            backgroundSize: '50px 50px'
          }}
        />
      </div>
    </section>
  )
}

export function PlaceholderScreen({
  title,
  description,
  imagePath,
  buttonText = "Go to Dashboard",
  buttonLink = "/dashboard",
}: PlaceholderScreenProps) {
  return (
    <div className="flex min-h-[80vh] flex-col items-center justify-center p-4 text-center">
      <Card className="mx-auto max-w-3xl">
        <CardHeader>
          <CardTitle className="text-3xl font-bold">{title}</CardTitle>
          <CardDescription className="text-lg">{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative mx-auto aspect-video w-full max-w-2xl overflow-hidden rounded-lg">
            <Image
              src={imagePath}
              alt="Coming Soon"
              fill
              className="object-cover"
              priority
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button asChild size="lg">
            <Link href={buttonLink}>{buttonText}</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}