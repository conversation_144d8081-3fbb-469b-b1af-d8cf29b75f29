"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { 
  ChevronLeft, 
  ChevronRight, 
  Home,
  FileText,
  Target,
  Settings,
  BarChart3,
  Wand2
} from "lucide-react"

import { SidebarNavItem } from "@/types"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/auth-context"
import { UserAvatar } from "@/components/user-avatar"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface EnhancedSidebarProps {
  items: SidebarNavItem[]
  isCollapsed: boolean
  onToggleCollapse: () => void
}

// Icon mapping for better control
const iconMap = {
  dashboard: Home,
  form: FileText,
  post: BarChart3,
  page: Target,
  settings: Settings,
} as const

export function EnhancedSidebar({ items, isCollapsed, onToggleCollapse }: EnhancedSidebarProps) {
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <TooltipProvider>
      <motion.div
        initial={false}
        animate={{ width: isCollapsed ? 80 : 280 }}
        transition={{
          duration: 0.3,
          ease: [0.25, 0.46, 0.45, 0.94]
        }}
        className={cn(
          // Always take full viewport height
          "flex flex-col h-screen w-full justify-between",
          // Premium gradient
          "bg-gradient-to-b from-[#F4F7FB] to-[#E8EBF0]",
          "border-r border-border/40 shadow-lg md:shadow-none",
          "backdrop-blur-xl supports-[backdrop-filter]:bg-background/80",
          "safe-top safe-bottom"
        )}
      >
        {/* Header with Logo and Toggle */}
        <div className="relative flex items-center justify-between px-6 py-6 border-b border-border/30">
          <AnimatePresence mode="wait">
            {!isCollapsed ? (
              <motion.div
                key="logo-expanded"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex items-center gap-3"
              >
                <Icons.logoFull className="h-8 w-auto max-w-[180px] text-foreground" />
              </motion.div>
            ) : (
              <motion.div
                key="logo-collapsed"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
                className="flex items-center justify-center mx-auto"
              >
                <Icons.logoIcon className="w-8 h-8 text-foreground" />
              </motion.div>
            )}
          </AnimatePresence>
          {/* Toggle Button (optional, can be hidden if not used) */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className={cn(
              "ml-2 w-6 h-6 rounded-full p-0 flex items-center justify-center",
              "bg-background/80 backdrop-blur-sm border border-border/50",
              "hover:bg-background transition-all duration-200"
            )}
          >
            {isCollapsed ? (
              <ChevronRight className="w-3 h-3 text-muted-foreground" />
            ) : (
              <ChevronLeft className="w-3 h-3 text-muted-foreground" />
            )}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-6 space-y-1 overflow-y-auto">
          <AnimatePresence>
            {items.map((item, index) => {
              const iconKey = item.icon as keyof typeof iconMap
              const Icon = iconMap[iconKey] || Home
              const isActive = pathname === item.href
              
              const linkContent = (
                <motion.div
                  layout
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={cn(
                    "relative flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-200 group",
                    "hover:bg-muted/50 hover:shadow-sm",
                    isActive && "bg-muted/60 border border-border/30",
                    item.disabled && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {/* Active indicator (minimal, no color bar) */}
                  {isActive && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="absolute left-0 top-0 bottom-0 w-1 bg-accent rounded-r-full"
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    />
                  )}
                  
                  <div className={cn(
                    "flex items-center justify-center w-5 h-5 transition-colors duration-200",
                    isActive ? "text-accent-foreground" : "text-muted-foreground group-hover:text-foreground"
                  )}>
                    <Icon className="w-8 h-8" />
                  </div>
                  
                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.span
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        transition={{ duration: 0.2 }}
                        className={cn(
                          "text-sm font-medium transition-colors duration-200",
                          isActive ? "text-foreground font-semibold" : "text-muted-foreground group-hover:text-foreground"
                        )}
                      >
                        {item.title}
                      </motion.span>
                    )}
                  </AnimatePresence>
                </motion.div>
              )

              if (isCollapsed) {
                return (
                  <Tooltip key={index} delayDuration={0}>
                    <TooltipTrigger asChild>
                      <Link 
                        href={item.disabled ? "#" : (item.href || "#")}
                        className="block"
                      >
                        {linkContent}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="font-medium">
                      {item.title}
                    </TooltipContent>
                  </Tooltip>
                )
              }

              return (
                <Link
                  key={index}
                  href={item.disabled ? "#" : (item.href || "#")}
                  className="block"
                >
                  {linkContent}
                </Link>
              )
            })}
          </AnimatePresence>
        </nav>

        {/* Footer with User */}
        <div className="px-3 py-2 border-t border-border/30">
          {!isCollapsed ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full flex items-center gap-3 px-3 py-2.5 rounded-xl hover:bg-background/50 transition-all duration-200 group"
                >
                  <UserAvatar
                    user={{ 
                      name: user?.name || null, 
                      image: null 
                    }}
                    className="w-8 h-8 ring-2 ring-background shadow-sm"
                  />
                  <div className="flex-1 text-left min-w-0">
                    <div className="text-sm font-medium text-foreground truncate">
                      {user?.name || 'User'}
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {user?.email}
                    </div>
                  </div>
                  <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                </motion.button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    <p className="font-medium">{user?.name}</p>
                    <p className="text-sm text-muted-foreground truncate">
                      {user?.email}
                    </p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">Dashboard</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="cursor-pointer text-red-600 focus:text-red-600"
                  onSelect={(event) => {
                    event.preventDefault()
                    logout()
                  }}
                >
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="w-full flex justify-center">
                      <UserAvatar
                        user={{ 
                          name: user?.name || null, 
                          image: null 
                        }}
                        className="w-8 h-8 ring-2 ring-background shadow-sm hover:ring-blue-200 transition-all duration-200"
                      />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <div className="flex items-center justify-start gap-2 p-2">
                      <div className="flex flex-col space-y-1 leading-none">
                        <p className="font-medium">{user?.name}</p>
                        <p className="text-sm text-muted-foreground truncate">
                          {user?.email}
                        </p>
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard">Dashboard</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard/settings">Settings</Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="cursor-pointer text-red-600 focus:text-red-600"
                      onSelect={(event) => {
                        event.preventDefault()
                        logout()
                      }}
                    >
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TooltipTrigger>
              <TooltipContent side="right" className="font-medium">
                {user?.name}
              </TooltipContent>
            </Tooltip>
          )}
          
          {/* Footer text */}
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ delay: 0.1 }}
                className="px-3"
              >
                <p className="text-xs text-muted-foreground text-center">
                  Powered by TractionX
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
