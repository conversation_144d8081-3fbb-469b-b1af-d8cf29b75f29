"use client"

import React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Plus, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { VisibilityCondition, ConditionClause, Question } from '@/lib/types/form';

// Condition validation schema
const conditionSchema = z.object({
  operator: z.enum(["and", "or", "not", "==", "!=", ">", "<", ">=", "<="]),
  conditions: z.array(
    z.object({
      question_id: z.string().min(1, 'Question is required'),
      value: z.any(),
      section_instance_index: z.number().optional(),
    })
  ).min(1, 'At least one condition is required'),
});

type ConditionFormValues = z.infer<typeof conditionSchema>;

interface ConditionBuilderDialogProps {
  condition?: VisibilityCondition;
  questions: Question[];
  currentQuestionId: string;
  onSave: (condition: VisibilityCondition) => void;
}

export function ConditionBuilderDialog({ condition, questions, currentQuestionId, onSave }: ConditionBuilderDialogProps) {
  const [open, setOpen] = React.useState(false);

  // Filter out the current question and only include Boolean and MCQ types
  const allowedTypes = ['boolean', 'single_select', 'multi_select'];
  const availableQuestions = questions.filter(q => 
    q.id !== currentQuestionId && allowedTypes.includes(q.type)
  );

  const form = useForm<ConditionFormValues>({
    resolver: zodResolver(conditionSchema),
    defaultValues: condition || {
      operator: "==",
      conditions: [{
        question_id: '',
        value: '',
        section_instance_index: undefined
      }],
    },
  });

  const onSubmit = (values: ConditionFormValues) => {
    // Ensure all conditions have a value (required by ConditionClause type)
    const processedValues: VisibilityCondition = {
      ...values,
      conditions: values.conditions.map(condition => ({
        ...condition,
        value: condition.value ?? '', // Provide empty string if value is undefined
      }))
    };
    onSave(processedValues);
    setOpen(false);
  };

  // Add a new condition
  const addCondition = () => {
    const currentConditions = form.getValues('conditions') || [];
    form.setValue('conditions', [...currentConditions, {
      question_id: '',
      value: '',
      section_instance_index: undefined
    }]);
  };

  // Remove a condition
  const removeCondition = (index: number) => {
    const currentConditions = form.getValues('conditions') || [];
    if (currentConditions.length > 1) {
      form.setValue('conditions', currentConditions.filter((_, i) => i !== index));
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Set Visibility Condition</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Visibility Condition</DialogTitle>
          <DialogDescription>
            Define when this question should be visible based on answers to other questions.
            Only Boolean (Yes/No) and Multiple Choice questions can be used for conditions.
          </DialogDescription>
        </DialogHeader>

        {availableQuestions.length === 0 ? (
          <div className="p-6 text-center">
            <div className="space-y-3">
              <div className="text-amber-600">
                <svg className="h-12 w-12 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">No Suitable Questions Available</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Conditional visibility only works with Boolean (Yes/No) and Multiple Choice (single/multi-select) questions.
                  Add these question types before this question to enable conditional visibility.
                </p>
              </div>
              <Button onClick={() => setOpen(false)} variant="outline">
                Close
              </Button>
            </div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">

              <FormField
                control={form.control}
                name="operator"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Operator</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select operator" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="==">Equals (==)</SelectItem>
                        <SelectItem value="!=">Not Equals (!=)</SelectItem>
                        <SelectItem value=">">Greater Than (&gt;)</SelectItem>
                        <SelectItem value="<">Less Than (&lt;)</SelectItem>
                        <SelectItem value=">=">Greater Than or Equal (&gt;=)</SelectItem>
                        <SelectItem value="<=">Less Than or Equal (&lt;=)</SelectItem>
                        <SelectItem value="and">AND (All conditions must be true)</SelectItem>
                        <SelectItem value="or">OR (Any condition can be true)</SelectItem>
                        <SelectItem value="not">NOT (Negate the condition)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      How to evaluate the condition
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <FormLabel>Conditions</FormLabel>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCondition}
                    className="h-8"
                  >
                    <Plus className="h-4 w-4 mr-1" /> Add Condition
                  </Button>
                </div>

                {form.watch('conditions')?.map((_, index) => (
                  <div key={index} className="flex flex-col space-y-2">
                    <div className="flex items-start space-x-2">
                      <div className="grid grid-cols-2 gap-2 flex-1">
                        <Select
                          onValueChange={(value) => form.setValue(`conditions.${index}.question_id`, value)}
                          defaultValue={form.getValues(`conditions.${index}.question_id`)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select question" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableQuestions.map((q) => (
                              <SelectItem key={q.id} value={q.id || ''}>
                                {q.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Input
                          placeholder="Expected value"
                          {...form.register(`conditions.${index}.value` as const)}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCondition(index)}
                        disabled={form.getValues('conditions')?.length <= 1}
                        className="text-destructive hover:text-destructive/90 hover:bg-destructive/10 h-10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Section instance index for repeatable sections */}
                    <div className="pl-4 border-l-2 border-muted">
                      <div className="flex items-center space-x-2">
                        <FormLabel className="text-sm">Section Instance Index (optional):</FormLabel>
                        <Input
                          type="number"
                          placeholder="Index"
                          className="w-24 h-8"
                          {...form.register(`conditions.${index}.section_instance_index` as const, {
                            valueAsNumber: true,
                            min: 0
                          })}
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        For repeatable sections, specify which instance to target (0-based index)
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <DialogFooter>
                <Button type="submit">Save Condition</Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
