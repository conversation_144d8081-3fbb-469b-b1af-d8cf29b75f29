<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test</h1>
    <button onclick="testCORS()">Test CORS Request</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Making request to:', 'http://localhost:8000/api/v1/onboard/46RPE6TP/validate');
                
                const response = await fetch('http://localhost:8000/api/v1/onboard/46RPE6TP/validate', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json, text/plain, */*',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                    },
                    credentials: 'include'
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <h3>✅ Success!</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Data:</strong> ${JSON.stringify(data, null, 2)}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ Error</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Status Text:</strong> ${response.statusText}</p>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Exception</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Type:</strong> ${error.name}</p>
                `;
            }
        }
        
        // Test on page load
        window.addEventListener('load', () => {
            console.log('Page loaded, current origin:', window.location.origin);
        });
    </script>
</body>
</html> 